# 弹珠游戏倍数选择器

## 概述

倍数选择器允许用户根据代币余额选择不同的倍数等级，实现快速使用代币而不是一个一个地使用。系统会根据用户的代币余额自动推荐合适的倍数，同时支持手动切换。

## 功能特性

### 🎯 自动倍数选择
- 根据代币余额自动选择最优倍数
- 新用户引导模式下固定使用 x1 倍数
- 智能推荐算法确保用户体验

### 🔄 手动倍数切换
- 支持循环切换可用倍数
- 实时验证代币余额
- 友好的错误提示

### 💰 余额管理
- 实时更新代币余额
- 自动调整可用倍数选项
- 余额不足时的智能处理

### 🎮 游戏集成
- 无缝集成到现有弹珠游戏架构
- 支持多弹珠发射
- 完整的动画和音效支持

## 倍数等级配置

| 倍数 | 所需代币 | 使用场景 |
|------|----------|----------|
| x1   | 1        | 新手引导、代币不足时 |
| x5   | 5        | 少量代币快速消耗 |
| x10  | 10       | 中等代币批量使用 |
| x20  | 20       | 大量代币快速消耗 |
| x50  | 50       | 最大倍数，快速清空代币 |

## 自动选择逻辑

```typescript
if (balance >= 50) return 50;
else if (balance >= 20) return 20;
else if (balance >= 10) return 10;
else if (balance >= 5) return 5;
else return 1;
```

**注意**: 新用户引导模式下始终返回 x1

## 快速开始

### 1. 基本使用

```typescript
import { MultiplierSelector } from './Service/MultiplierSelector';

// 创建倍数选择器
const selector = new MultiplierSelector(25); // 用户有25个代币

// 获取自动选择的倍数
console.log(selector.getCurrentMultiplier()); // 20

// 获取可用倍数列表
console.log(selector.getAvailableMultipliers()); // [1, 5, 10, 20]
```

### 2. 游戏集成

```typescript
import { MultiplierController } from './Controller/MultiplierController';

// 创建倍数控制器
const multiplierController = new MultiplierController({
    initialTokenBalance: 30,
    isNewUserGuide: false,
    onMultiplierChanged: (newMultiplier) => {
        console.log(`倍数切换到: x${newMultiplier}`);
    },
    onBalanceChanged: (newBalance) => {
        console.log(`余额更新为: ${newBalance}`);
    }
});

// 用户点击倍数切换按钮
multiplierController.onMultiplierButtonClick();

// 发射弹珠前检查
if (multiplierController.canLaunchBall()) {
    multiplierController.consumeTokensForLaunch();
    multiplierController.createMultipleBalls(launchPower);
}
```

### 3. 新用户引导

```typescript
// 新用户引导模式
const guideSelector = new MultiplierSelector(100, true);
console.log(guideSelector.getCurrentMultiplier()); // 1 (固定x1)

// 退出引导模式
guideSelector.setNewUserGuideMode(false);
console.log(guideSelector.getCurrentMultiplier()); // 50 (自动选择)
```

## API 参考

### MultiplierSelector

#### 构造函数
```typescript
constructor(tokenBalance: number, isNewUserGuide?: boolean)
```

#### 主要方法
- `getCurrentMultiplier(): number` - 获取当前倍数
- `getAvailableMultipliers(): number[]` - 获取可用倍数列表
- `setMultiplier(multiplier: number): boolean` - 手动设置倍数
- `updateTokenBalance(newBalance: number): void` - 更新代币余额
- `cycleToNextMultiplier(): number` - 循环切换到下一个倍数
- `canUseMultiplier(multiplier: number): boolean` - 检查是否可使用指定倍数

### MultiplierController

#### 构造函数
```typescript
constructor(config: IMultiplierControllerConfig)
```

#### 主要方法
- `onMultiplierButtonClick(): void` - 处理倍数切换按钮点击
- `canLaunchBall(): boolean` - 检查是否可以发射弹珠
- `consumeTokensForLaunch(): boolean` - 消耗代币进行发射
- `createMultipleBalls(launchPower: number): void` - 创建多个弹珠

## 测试

运行测试套件：

```bash
# 运行所有测试
npm test MultiplierSelector.test.ts

# 运行示例
npm run example MultiplierSelector.example.ts
```

测试覆盖：
- ✅ 自动倍数选择逻辑
- ✅ 新用户引导模式
- ✅ 手动倍数设置
- ✅ 余额更新处理
- ✅ 可用倍数获取
- ✅ 循环切换功能
- ✅ 工具函数验证

## 最佳实践

### 1. 错误处理
```typescript
// 检查设置结果
if (!selector.setMultiplier(50)) {
    // 处理设置失败的情况
    showErrorMessage("代币不足，无法使用x50倍数");
}
```

### 2. 用户体验
```typescript
// 提供倍数建议
const suggestion = selector.getMultiplierSuggestion();
showTooltip(suggestion);

// 显示可用倍数
const levels = selector.getMultiplierLevels();
levels.forEach(level => {
    updateButtonState(level.value, level.isAvailable);
});
```

### 3. 性能优化
```typescript
// 批量更新UI
const config = selector.getConfig();
updateUI({
    currentMultiplier: config.currentMultiplier,
    tokenBalance: config.tokenBalance,
    availableMultipliers: selector.getAvailableMultipliers()
});
```

## 注意事项

1. **新用户引导**: 新用户引导模式下，无论代币余额多少都固定使用 x1 倍数
2. **余额验证**: 所有倍数设置都会进行余额验证，确保用户有足够代币
3. **自动调整**: 当余额减少导致当前倍数不可用时，系统会自动调整到合适的倍数
4. **循环切换**: 倍数切换采用循环模式，到达最大值后会回到最小值

## 更新日志

### v1.0.0
- ✨ 初始版本发布
- ✨ 支持自动倍数选择
- ✨ 支持新用户引导模式
- ✨ 完整的测试覆盖
- ✨ 游戏集成支持
