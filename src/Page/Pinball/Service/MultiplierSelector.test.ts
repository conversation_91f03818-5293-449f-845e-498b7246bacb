/**
 * MultiplierSelector 测试文件
 * 验证倍数选择器的各项功能
 */

import { MultiplierSelector, getRecommendedMultiplier } from './MultiplierSelector';

/**
 * 测试自动倍数选择逻辑
 */
function testAutoMultiplierSelection() {
    console.log('=== 测试自动倍数选择 ===');
    
    const testCases = [
        { balance: 100, expected: 50, description: '余额100应选择x50' },
        { balance: 50, expected: 50, description: '余额50应选择x50' },
        { balance: 49, expected: 20, description: '余额49应选择x20' },
        { balance: 20, expected: 20, description: '余额20应选择x20' },
        { balance: 19, expected: 10, description: '余额19应选择x10' },
        { balance: 10, expected: 10, description: '余额10应选择x10' },
        { balance: 9, expected: 5, description: '余额9应选择x5' },
        { balance: 5, expected: 5, description: '余额5应选择x5' },
        { balance: 4, expected: 1, description: '余额4应选择x1' },
        { balance: 1, expected: 1, description: '余额1应选择x1' },
        { balance: 0, expected: 1, description: '余额0应选择x1' },
    ];
    
    let passedTests = 0;
    
    testCases.forEach((testCase, index) => {
        const selector = new MultiplierSelector(testCase.balance);
        const actual = selector.getCurrentMultiplier();
        const passed = actual === testCase.expected;
        
        console.log(`测试 ${index + 1}: ${testCase.description} - ${passed ? '✅ 通过' : '❌ 失败'}`);
        if (!passed) {
            console.log(`  期望: x${testCase.expected}, 实际: x${actual}`);
        } else {
            passedTests++;
        }
    });
    
    console.log(`自动选择测试结果: ${passedTests}/${testCases.length} 通过\n`);
    return passedTests === testCases.length;
}

/**
 * 测试新用户引导模式
 */
function testNewUserGuideMode() {
    console.log('=== 测试新用户引导模式 ===');
    
    let allPassed = true;
    
    // 测试1: 新用户引导模式下始终选择x1
    const selector1 = new MultiplierSelector(100, true);
    const test1Passed = selector1.getCurrentMultiplier() === 1;
    console.log(`测试1: 新用户引导模式(余额100) - ${test1Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test1Passed) {
        console.log(`  期望: x1, 实际: x${selector1.getCurrentMultiplier()}`);
        allPassed = false;
    }
    
    // 测试2: 退出引导模式后自动选择
    selector1.setNewUserGuideMode(false);
    const test2Passed = selector1.getCurrentMultiplier() === 50;
    console.log(`测试2: 退出引导模式后自动选择 - ${test2Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test2Passed) {
        console.log(`  期望: x50, 实际: x${selector1.getCurrentMultiplier()}`);
        allPassed = false;
    }
    
    // 测试3: 重新进入引导模式
    selector1.setNewUserGuideMode(true);
    const test3Passed = selector1.getCurrentMultiplier() === 1;
    console.log(`测试3: 重新进入引导模式 - ${test3Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test3Passed) {
        console.log(`  期望: x1, 实际: x${selector1.getCurrentMultiplier()}`);
        allPassed = false;
    }
    
    console.log(`新用户引导测试结果: ${allPassed ? '全部通过' : '存在失败'}\n`);
    return allPassed;
}

/**
 * 测试手动设置倍数
 */
function testManualMultiplierSetting() {
    console.log('=== 测试手动设置倍数 ===');
    
    const selector = new MultiplierSelector(25);
    let allPassed = true;
    
    // 测试1: 设置有效倍数
    const test1Passed = selector.setMultiplier(10) && selector.getCurrentMultiplier() === 10;
    console.log(`测试1: 设置有效倍数x10 - ${test1Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test1Passed) allPassed = false;
    
    // 测试2: 设置超出余额的倍数
    const test2Passed = !selector.setMultiplier(50) && selector.getCurrentMultiplier() === 10;
    console.log(`测试2: 设置超出余额的倍数x50 - ${test2Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test2Passed) allPassed = false;
    
    // 测试3: 设置无效倍数
    const test3Passed = !selector.setMultiplier(7) && selector.getCurrentMultiplier() === 10;
    console.log(`测试3: 设置无效倍数x7 - ${test3Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test3Passed) allPassed = false;
    
    console.log(`手动设置测试结果: ${allPassed ? '全部通过' : '存在失败'}\n`);
    return allPassed;
}

/**
 * 测试余额更新
 */
function testBalanceUpdate() {
    console.log('=== 测试余额更新 ===');
    
    const selector = new MultiplierSelector(30);
    let allPassed = true;
    
    // 初始状态应该是x20
    const initialTest = selector.getCurrentMultiplier() === 20;
    console.log(`初始状态测试 - ${initialTest ? '✅ 通过' : '❌ 失败'}`);
    if (!initialTest) allPassed = false;
    
    // 测试1: 余额减少，倍数应该自动调整
    selector.updateTokenBalance(8);
    const test1Passed = selector.getCurrentMultiplier() === 5;
    console.log(`测试1: 余额减少到8，倍数调整 - ${test1Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test1Passed) {
        console.log(`  期望: x5, 实际: x${selector.getCurrentMultiplier()}`);
        allPassed = false;
    }
    
    // 测试2: 余额增加，倍数保持不变（不自动增加）
    selector.updateTokenBalance(40);
    const test2Passed = selector.getCurrentMultiplier() === 5;
    console.log(`测试2: 余额增加到40，倍数保持 - ${test2Passed ? '✅ 通过' : '❌ 失败'}`);
    if (!test2Passed) {
        console.log(`  期望: x5, 实际: x${selector.getCurrentMultiplier()}`);
        allPassed = false;
    }
    
    console.log(`余额更新测试结果: ${allPassed ? '全部通过' : '存在失败'}\n`);
    return allPassed;
}

/**
 * 测试可用倍数获取
 */
function testAvailableMultipliers() {
    console.log('=== 测试可用倍数获取 ===');
    
    const testCases = [
        { balance: 60, expected: [1, 5, 10, 20, 50] },
        { balance: 25, expected: [1, 5, 10, 20] },
        { balance: 7, expected: [1, 5] },
        { balance: 2, expected: [1] },
        { balance: 0, expected: [] },
    ];
    
    let passedTests = 0;
    
    testCases.forEach((testCase, index) => {
        const selector = new MultiplierSelector(testCase.balance);
        const actual = selector.getAvailableMultipliers();
        const passed = JSON.stringify(actual) === JSON.stringify(testCase.expected);
        
        console.log(`测试 ${index + 1}: 余额${testCase.balance}的可用倍数 - ${passed ? '✅ 通过' : '❌ 失败'}`);
        if (!passed) {
            console.log(`  期望: [${testCase.expected.join(', ')}], 实际: [${actual.join(', ')}]`);
        } else {
            passedTests++;
        }
    });
    
    console.log(`可用倍数测试结果: ${passedTests}/${testCases.length} 通过\n`);
    return passedTests === testCases.length;
}

/**
 * 测试循环切换倍数
 */
function testCycleMultiplier() {
    console.log('=== 测试循环切换倍数 ===');
    
    const selector = new MultiplierSelector(15); // 可用倍数: [1, 5, 10]
    let allPassed = true;
    
    // 初始应该是x10
    const initialMultiplier = selector.getCurrentMultiplier();
    console.log(`初始倍数: x${initialMultiplier}`);
    
    // 循环切换测试
    const expectedSequence = [1, 5, 10, 1, 5]; // 从x10开始，循环切换
    const actualSequence = [];
    
    for (let i = 0; i < 5; i++) {
        const newMultiplier = selector.cycleToNextMultiplier();
        actualSequence.push(newMultiplier);
    }
    
    const sequenceMatched = JSON.stringify(actualSequence) === JSON.stringify(expectedSequence);
    console.log(`循环切换序列测试 - ${sequenceMatched ? '✅ 通过' : '❌ 失败'}`);
    if (!sequenceMatched) {
        console.log(`  期望序列: [${expectedSequence.join(', ')}]`);
        console.log(`  实际序列: [${actualSequence.join(', ')}]`);
        allPassed = false;
    }
    
    console.log(`循环切换测试结果: ${allPassed ? '全部通过' : '存在失败'}\n`);
    return allPassed;
}

/**
 * 测试工具函数
 */
function testUtilityFunctions() {
    console.log('=== 测试工具函数 ===');
    
    const testCases = [
        { balance: 75, isGuide: false, expected: 50 },
        { balance: 15, isGuide: false, expected: 10 },
        { balance: 100, isGuide: true, expected: 1 },
        { balance: 3, isGuide: false, expected: 1 },
    ];
    
    let passedTests = 0;
    
    testCases.forEach((testCase, index) => {
        const actual = getRecommendedMultiplier(testCase.balance, testCase.isGuide);
        const passed = actual === testCase.expected;
        
        const guideText = testCase.isGuide ? '(引导模式)' : '';
        console.log(`测试 ${index + 1}: 余额${testCase.balance}${guideText} - ${passed ? '✅ 通过' : '❌ 失败'}`);
        if (!passed) {
            console.log(`  期望: x${testCase.expected}, 实际: x${actual}`);
        } else {
            passedTests++;
        }
    });
    
    console.log(`工具函数测试结果: ${passedTests}/${testCases.length} 通过\n`);
    return passedTests === testCases.length;
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    console.log('🧪 开始运行 MultiplierSelector 测试套件\n');
    
    const testResults = [
        testAutoMultiplierSelection(),
        testNewUserGuideMode(),
        testManualMultiplierSetting(),
        testBalanceUpdate(),
        testAvailableMultipliers(),
        testCycleMultiplier(),
        testUtilityFunctions(),
    ];
    
    const passedCount = testResults.filter(result => result).length;
    const totalCount = testResults.length;
    
    console.log('📊 测试总结:');
    console.log(`总测试数: ${totalCount}`);
    console.log(`通过测试: ${passedCount}`);
    console.log(`失败测试: ${totalCount - passedCount}`);
    console.log(`通过率: ${((passedCount / totalCount) * 100).toFixed(1)}%`);
    
    if (passedCount === totalCount) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  存在测试失败，请检查代码');
    }
    
    return passedCount === totalCount;
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
    runAllTests();
}
