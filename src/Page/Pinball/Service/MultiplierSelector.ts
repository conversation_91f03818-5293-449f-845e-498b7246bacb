/**
 * 弹珠游戏倍数选择器
 * 根据用户代币余额自动选择合适的倍数等级
 */

export interface IMultiplierConfig {
    /** 可用的倍数等级列表 */
    availableLevels: Array<number>;
    /** 当前选中的倍数 */
    currentMultiplier: number;
    /** 用户代币余额 */
    tokenBalance: number;
    /** 是否为新用户引导模式 */
    isNewUserGuide: boolean;
}

export interface IMultiplierLevel {
    /** 倍数值 */
    value: number;
    /** 所需最小代币数量 */
    requiredTokens: number;
    /** 是否可用 */
    isAvailable: boolean;
    /** 显示文本 */
    displayText: string;
}

export class MultiplierSelector {
    private config: IMultiplierConfig;
    private readonly DEFAULT_LEVELS = [1, 5, 10, 20, 50];

    constructor(tokenBalance: number, isNewUserGuide = false) {
        this.config = {
            availableLevels: [...this.DEFAULT_LEVELS],
            currentMultiplier: 1,
            tokenBalance,
            isNewUserGuide
        };

        // 初始化时自动选择倍数
        this.autoSelectMultiplier();
    }

    /**
     * 根据代币余额自动选择倍数
     * 新用户引导模式下不自动选择，保持默认值1
     */
    public autoSelectMultiplier(): number {
        if (this.config.isNewUserGuide) {
            this.config.currentMultiplier = 1;
            return 1;
        }

        const balance = this.config.tokenBalance;
        let selectedMultiplier = 1;

        if (balance >= 50) {
            selectedMultiplier = 50;
        } else if (balance >= 20) {
            selectedMultiplier = 20;
        } else if (balance >= 10) {
            selectedMultiplier = 10;
        } else if (balance >= 5) {
            selectedMultiplier = 5;
        } else {
            selectedMultiplier = 1;
        }

        this.config.currentMultiplier = selectedMultiplier;
        return selectedMultiplier;
    }

    /**
     * 获取所有倍数等级的详细信息
     */
    public getMultiplierLevels(): Array<IMultiplierLevel> {
        return this.config.availableLevels.map(level => ({
            value: level,
            requiredTokens: level,
            isAvailable: this.config.tokenBalance >= level,
            displayText: `x${level}`
        }));
    }

    /**
     * 获取可用的倍数等级
     */
    public getAvailableMultipliers(): Array<number> {
        return this.config.availableLevels.filter(
            level => this.config.tokenBalance >= level
        );
    }

    /**
     * 手动设置倍数
     * @param multiplier 要设置的倍数值
     * @returns 是否设置成功
     */
    public setMultiplier(multiplier: number): boolean {
        // 检查倍数是否在可用列表中
        if (!this.config.availableLevels.includes(multiplier)) {
            console.warn(`Invalid multiplier: ${multiplier}. Available levels: ${this.config.availableLevels}`);
            return false;
        }

        // 检查代币余额是否足够
        if (this.config.tokenBalance < multiplier) {
            console.warn(`Insufficient tokens. Required: ${multiplier}, Available: ${this.config.tokenBalance}`);
            return false;
        }

        this.config.currentMultiplier = multiplier;
        return true;
    }

    /**
     * 更新代币余额并重新计算可用倍数
     * @param newBalance 新的代币余额
     */
    public updateTokenBalance(newBalance: number): void {
        this.config.tokenBalance = newBalance;

        // 如果当前选中的倍数超过了新余额，自动调整
        if (this.config.currentMultiplier > newBalance) {
            this.autoSelectMultiplier();
        }
    }

    /**
     * 检查是否可以使用指定倍数
     * @param multiplier 要检查的倍数
     */
    public canUseMultiplier(multiplier: number): boolean {
        return this.config.tokenBalance >= multiplier &&
               this.config.availableLevels.includes(multiplier);
    }

    /**
     * 获取下一个可用的倍数等级
     */
    public getNextAvailableMultiplier(): number | null {
        const availableMultipliers = this.getAvailableMultipliers();
        const currentIndex = availableMultipliers.indexOf(this.config.currentMultiplier);

        if (currentIndex === -1 || currentIndex === availableMultipliers.length - 1) {
            return null; // 当前倍数不在列表中或已是最大值
        }

        return availableMultipliers[currentIndex + 1];
    }

    /**
     * 获取上一个可用的倍数等级
     */
    public getPreviousAvailableMultiplier(): number | null {
        const availableMultipliers = this.getAvailableMultipliers();
        const currentIndex = availableMultipliers.indexOf(this.config.currentMultiplier);

        if (currentIndex <= 0) {
            return null; // 当前倍数不在列表中或已是最小值
        }

        return availableMultipliers[currentIndex - 1];
    }

    /**
     * 循环切换到下一个倍数
     */
    public cycleToNextMultiplier(): number {
        const availableMultipliers = this.getAvailableMultipliers();
        if (availableMultipliers.length === 0) {
            return 1;
        }

        const currentIndex = availableMultipliers.indexOf(this.config.currentMultiplier);
        const nextIndex = (currentIndex + 1) % availableMultipliers.length;
        const nextMultiplier = availableMultipliers[nextIndex];

        this.config.currentMultiplier = nextMultiplier;
        return nextMultiplier;
    }

    /**
     * 设置新用户引导模式
     * @param isGuide 是否为引导模式
     */
    public setNewUserGuideMode(isGuide: boolean): void {
        this.config.isNewUserGuide = isGuide;
        if (isGuide) {
            this.config.currentMultiplier = 1;
        } else {
            this.autoSelectMultiplier();
        }
    }

    /**
     * 获取当前配置
     */
    public getConfig(): Readonly<IMultiplierConfig> {
        return { ...this.config };
    }

    /**
     * 获取当前选中的倍数
     */
    public getCurrentMultiplier(): number {
        return this.config.currentMultiplier;
    }

    /**
     * 获取当前代币余额
     */
    public getTokenBalance(): number {
        return this.config.tokenBalance;
    }

    /**
     * 是否为新用户引导模式
     */
    public isInNewUserGuideMode(): boolean {
        return this.config.isNewUserGuide;
    }

    /**
     * 获取倍数选择的建议信息
     */
    public getMultiplierSuggestion(): string {
        const balance = this.config.tokenBalance;
        const current = this.config.currentMultiplier;

        if (this.config.isNewUserGuide) {
            return '新手引导模式，建议使用 x1 倍数';
        }

        if (balance < 5) {
            return '代币较少，建议使用 x1 倍数';
        } else if (balance >= 50) {
            return `代币充足，建议使用 x${Math.min(50, balance)} 倍数快速游戏`;
        } else {
            const suggested = this.autoSelectMultiplier();
            return `根据当前余额 ${balance}，建议使用 x${suggested} 倍数`;
        }
    }
}

/**
 * 倍数选择器工厂函数
 * @param tokenBalance 代币余额
 * @param isNewUserGuide 是否为新用户引导
 * @returns MultiplierSelector实例
 */
export function createMultiplierSelector(
    tokenBalance: number,
    isNewUserGuide = false
): MultiplierSelector {
    return new MultiplierSelector(tokenBalance, isNewUserGuide);
}

/**
 * 快速获取推荐倍数的工具函数
 * @param tokenBalance 代币余额
 * @param isNewUserGuide 是否为新用户引导
 * @returns 推荐的倍数值
 */
export function getRecommendedMultiplier(
    tokenBalance: number,
    isNewUserGuide = false
): number {
    if (isNewUserGuide) {
        return 1;
    }

    if (tokenBalance >= 50) return 50;
    if (tokenBalance >= 20) return 20;
    if (tokenBalance >= 10) return 10;
    if (tokenBalance >= 5) return 5;
    return 1;
}
