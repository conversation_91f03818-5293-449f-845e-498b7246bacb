/**
 * MultiplierSelector 使用示例
 * 展示如何在弹珠游戏中使用倍数选择器
 */

import { MultiplierSelector, createMultiplierSelector, getRecommendedMultiplier } from './MultiplierSelector';

/**
 * 示例1: 基本使用
 */
export function basicUsageExample() {
    console.log('=== 基本使用示例 ===');
    
    // 创建倍数选择器，用户有25个代币
    const selector = new MultiplierSelector(25);
    
    console.log('当前代币余额:', selector.getTokenBalance()); // 25
    console.log('自动选择的倍数:', selector.getCurrentMultiplier()); // 20 (因为25 >= 20)
    console.log('可用倍数列表:', selector.getAvailableMultipliers()); // [1, 5, 10, 20]
    console.log('推荐信息:', selector.getMultiplierSuggestion());
}

/**
 * 示例2: 新用户引导模式
 */
export function newUserGuideExample() {
    console.log('\n=== 新用户引导模式示例 ===');
    
    // 新用户引导模式，即使有足够代币也使用x1
    const selector = new MultiplierSelector(100, true);
    
    console.log('新用户引导模式:', selector.isInNewUserGuideMode()); // true
    console.log('当前倍数:', selector.getCurrentMultiplier()); // 1
    console.log('推荐信息:', selector.getMultiplierSuggestion());
    
    // 退出新用户引导模式
    selector.setNewUserGuideMode(false);
    console.log('退出引导后的倍数:', selector.getCurrentMultiplier()); // 50
}

/**
 * 示例3: 手动切换倍数
 */
export function manualSwitchExample() {
    console.log('\n=== 手动切换倍数示例 ===');
    
    const selector = new MultiplierSelector(30);
    console.log('初始倍数:', selector.getCurrentMultiplier()); // 20
    
    // 手动设置倍数
    const success1 = selector.setMultiplier(10);
    console.log('设置为x10:', success1, '当前倍数:', selector.getCurrentMultiplier()); // true, 10
    
    // 尝试设置超出余额的倍数
    const success2 = selector.setMultiplier(50);
    console.log('设置为x50:', success2, '当前倍数:', selector.getCurrentMultiplier()); // false, 10
    
    // 循环切换倍数
    console.log('循环切换倍数:');
    for (let i = 0; i < 5; i++) {
        const newMultiplier = selector.cycleToNextMultiplier();
        console.log(`  第${i + 1}次切换:`, newMultiplier);
    }
}

/**
 * 示例4: 余额更新处理
 */
export function balanceUpdateExample() {
    console.log('\n=== 余额更新处理示例 ===');
    
    const selector = new MultiplierSelector(60);
    console.log('初始状态 - 余额:', selector.getTokenBalance(), '倍数:', selector.getCurrentMultiplier());
    
    // 消耗代币后更新余额
    selector.updateTokenBalance(15);
    console.log('更新余额后 - 余额:', selector.getTokenBalance(), '倍数:', selector.getCurrentMultiplier());
    
    // 再次消耗代币
    selector.updateTokenBalance(3);
    console.log('再次更新后 - 余额:', selector.getTokenBalance(), '倍数:', selector.getCurrentMultiplier());
    
    // 获得更多代币
    selector.updateTokenBalance(40);
    console.log('获得代币后 - 余额:', selector.getTokenBalance(), '倍数:', selector.getCurrentMultiplier());
}

/**
 * 示例5: 获取详细的倍数等级信息
 */
export function detailedLevelsExample() {
    console.log('\n=== 详细倍数等级信息示例 ===');
    
    const selector = new MultiplierSelector(12);
    const levels = selector.getMultiplierLevels();
    
    console.log('所有倍数等级信息:');
    levels.forEach(level => {
        console.log(`  ${level.displayText}: 需要${level.requiredTokens}代币, ${level.isAvailable ? '可用' : '不可用'}`);
    });
}

/**
 * 示例6: 工厂函数和工具函数使用
 */
export function utilityFunctionsExample() {
    console.log('\n=== 工厂函数和工具函数示例 ===');
    
    // 使用工厂函数创建
    const selector = createMultiplierSelector(35, false);
    console.log('工厂函数创建的选择器倍数:', selector.getCurrentMultiplier());
    
    // 使用工具函数快速获取推荐倍数
    const recommended1 = getRecommendedMultiplier(75);
    const recommended2 = getRecommendedMultiplier(8);
    const recommended3 = getRecommendedMultiplier(100, true); // 新用户引导
    
    console.log('75代币推荐倍数:', recommended1); // 50
    console.log('8代币推荐倍数:', recommended2);  // 5
    console.log('新用户推荐倍数:', recommended3); // 1
}

/**
 * 示例7: 在游戏中的实际应用
 */
export function gameIntegrationExample() {
    console.log('\n=== 游戏集成示例 ===');
    
    // 模拟游戏场景
    class PinballGame {
        private multiplierSelector: MultiplierSelector;
        
        constructor(userTokenBalance: number, isNewUser: boolean = false) {
            this.multiplierSelector = new MultiplierSelector(userTokenBalance, isNewUser);
            console.log(`游戏初始化 - 余额: ${userTokenBalance}, 自动选择倍数: x${this.multiplierSelector.getCurrentMultiplier()}`);
        }
        
        // 用户点击倍数切换按钮
        public onMultiplierButtonClick(): void {
            const newMultiplier = this.multiplierSelector.cycleToNextMultiplier();
            console.log(`用户切换倍数到: x${newMultiplier}`);
        }
        
        // 检查是否可以发射
        public canLaunch(): boolean {
            const currentMultiplier = this.multiplierSelector.getCurrentMultiplier();
            const canUse = this.multiplierSelector.canUseMultiplier(currentMultiplier);
            console.log(`检查发射条件 - 倍数: x${currentMultiplier}, 可以发射: ${canUse}`);
            return canUse;
        }
        
        // 发射弹珠（消耗代币）
        public launchBall(): boolean {
            if (!this.canLaunch()) {
                console.log('代币不足，无法发射');
                return false;
            }
            
            const multiplier = this.multiplierSelector.getCurrentMultiplier();
            const newBalance = this.multiplierSelector.getTokenBalance() - multiplier;
            
            console.log(`发射弹珠 - 消耗 ${multiplier} 代币`);
            this.multiplierSelector.updateTokenBalance(newBalance);
            console.log(`剩余代币: ${this.multiplierSelector.getTokenBalance()}, 当前倍数: x${this.multiplierSelector.getCurrentMultiplier()}`);
            
            return true;
        }
        
        // 获得奖励代币
        public earnTokens(amount: number): void {
            const newBalance = this.multiplierSelector.getTokenBalance() + amount;
            this.multiplierSelector.updateTokenBalance(newBalance);
            console.log(`获得 ${amount} 代币奖励，当前余额: ${this.multiplierSelector.getTokenBalance()}`);
        }
    }
    
    // 模拟游戏流程
    const game = new PinballGame(45);
    
    game.onMultiplierButtonClick(); // 切换倍数
    game.launchBall(); // 发射弹珠
    game.launchBall(); // 再次发射
    game.earnTokens(30); // 获得奖励
    game.launchBall(); // 使用新的倍数发射
}

/**
 * 运行所有示例
 */
export function runAllExamples() {
    basicUsageExample();
    newUserGuideExample();
    manualSwitchExample();
    balanceUpdateExample();
    detailedLevelsExample();
    utilityFunctionsExample();
    gameIntegrationExample();
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
    runAllExamples();
}
