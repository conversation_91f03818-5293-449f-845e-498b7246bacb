/**
 * 弹珠游戏倍数控制器
 * 集成MultiplierSelector到现有的弹珠游戏架构中
 */

import { MultiplierSelector, IMultiplierLevel } from '../Service/MultiplierSelector';
import { PhysicsUtil } from '../Service/PhysicsUtil';

export interface IMultiplierControllerConfig {
    /** 初始代币余额 */
    initialTokenBalance: number;
    /** 是否为新用户引导模式 */
    isNewUserGuide?: boolean;
    /** 倍数变更回调 */
    onMultiplierChanged?: (newMultiplier: number) => void;
    /** 余额变更回调 */
    onBalanceChanged?: (newBalance: number) => void;
    /** 倍数不可用回调 */
    onMultiplierUnavailable?: (requiredTokens: number, currentBalance: number) => void;
}

export class MultiplierController {
    private multiplierSelector: MultiplierSelector;
    private config: IMultiplierControllerConfig;
    private view: any; // 弹珠游戏视图引用
    
    constructor(config: IMultiplierControllerConfig) {
        this.config = config;
        this.multiplierSelector = new MultiplierSelector(
            config.initialTokenBalance,
            config.isNewUserGuide || false
        );
        
        // 初始化时触发回调
        this.notifyMultiplierChanged();
        this.notifyBalanceChanged();
    }
    
    /**
     * 设置游戏视图引用
     * @param view 弹珠游戏视图对象
     */
    public setView(view: any): void {
        this.view = view;
    }
    
    /**
     * 获取当前选中的倍数
     */
    public getCurrentMultiplier(): number {
        return this.multiplierSelector.getCurrentMultiplier();
    }
    
    /**
     * 获取当前代币余额
     */
    public getTokenBalance(): number {
        return this.multiplierSelector.getTokenBalance();
    }
    
    /**
     * 获取所有倍数等级信息（用于UI显示）
     */
    public getMultiplierLevels(): IMultiplierLevel[] {
        return this.multiplierSelector.getMultiplierLevels();
    }
    
    /**
     * 获取可用的倍数列表
     */
    public getAvailableMultipliers(): number[] {
        return this.multiplierSelector.getAvailableMultipliers();
    }
    
    /**
     * 用户点击倍数切换按钮
     */
    public onMultiplierButtonClick(): void {
        const oldMultiplier = this.getCurrentMultiplier();
        const newMultiplier = this.multiplierSelector.cycleToNextMultiplier();
        
        if (newMultiplier !== oldMultiplier) {
            this.notifyMultiplierChanged();
            
            // 播放切换音效
            if (this.view && this.view.playSound) {
                this.view.playSound('multiplier_switch');
            }
            
            // 播放切换动画
            this.playMultiplierSwitchAnimation();
        }
    }
    
    /**
     * 手动设置倍数
     * @param multiplier 要设置的倍数值
     * @returns 是否设置成功
     */
    public setMultiplier(multiplier: number): boolean {
        const oldMultiplier = this.getCurrentMultiplier();
        const success = this.multiplierSelector.setMultiplier(multiplier);
        
        if (success && multiplier !== oldMultiplier) {
            this.notifyMultiplierChanged();
            this.playMultiplierSwitchAnimation();
        } else if (!success) {
            // 设置失败，触发不可用回调
            const currentBalance = this.getTokenBalance();
            if (this.config.onMultiplierUnavailable) {
                this.config.onMultiplierUnavailable(multiplier, currentBalance);
            }
            
            // 播放错误提示音效
            if (this.view && this.view.playSound) {
                this.view.playSound('error_beep');
            }
        }
        
        return success;
    }
    
    /**
     * 检查是否可以发射弹珠
     * @returns 是否可以发射
     */
    public canLaunchBall(): boolean {
        const currentMultiplier = this.getCurrentMultiplier();
        return this.multiplierSelector.canUseMultiplier(currentMultiplier);
    }
    
    /**
     * 发射弹珠前的代币消耗
     * @returns 是否消耗成功
     */
    public consumeTokensForLaunch(): boolean {
        if (!this.canLaunchBall()) {
            return false;
        }
        
        const multiplier = this.getCurrentMultiplier();
        const newBalance = this.getTokenBalance() - multiplier;
        
        this.updateTokenBalance(newBalance);
        return true;
    }
    
    /**
     * 更新代币余额
     * @param newBalance 新的余额
     */
    public updateTokenBalance(newBalance: number): void {
        const oldBalance = this.getTokenBalance();
        this.multiplierSelector.updateTokenBalance(newBalance);
        
        if (newBalance !== oldBalance) {
            this.notifyBalanceChanged();
            
            // 如果余额增加，播放获得代币音效
            if (newBalance > oldBalance && this.view && this.view.playSound) {
                this.view.playSound('token_earned');
            }
        }
    }
    
    /**
     * 设置新用户引导模式
     * @param isGuide 是否为引导模式
     */
    public setNewUserGuideMode(isGuide: boolean): void {
        const oldMultiplier = this.getCurrentMultiplier();
        this.multiplierSelector.setNewUserGuideMode(isGuide);
        
        const newMultiplier = this.getCurrentMultiplier();
        if (newMultiplier !== oldMultiplier) {
            this.notifyMultiplierChanged();
        }
    }
    
    /**
     * 获取倍数选择建议
     */
    public getMultiplierSuggestion(): string {
        return this.multiplierSelector.getMultiplierSuggestion();
    }
    
    /**
     * 获取下一个可用倍数
     */
    public getNextAvailableMultiplier(): number | null {
        return this.multiplierSelector.getNextAvailableMultiplier();
    }
    
    /**
     * 获取上一个可用倍数
     */
    public getPreviousAvailableMultiplier(): number | null {
        return this.multiplierSelector.getPreviousAvailableMultiplier();
    }
    
    /**
     * 是否处于新用户引导模式
     */
    public isInNewUserGuideMode(): boolean {
        return this.multiplierSelector.isInNewUserGuideMode();
    }
    
    /**
     * 创建多个弹珠（根据当前倍数）
     * 集成到现有的弹珠发射逻辑中
     */
    public createMultipleBalls(launchPower: number): void {
        const multiplier = this.getCurrentMultiplier();
        const baseDelay = 100; // 每个弹珠间隔100ms
        
        for (let i = 0; i < multiplier; i++) {
            setTimeout(() => {
                this.createSingleBall(launchPower, i);
            }, i * baseDelay);
        }
    }
    
    /**
     * 创建单个弹珠
     * @param launchPower 发射力度
     * @param ballIndex 弹珠索引（用于轻微偏移）
     */
    private createSingleBall(launchPower: number, ballIndex: number): void {
        if (!this.view || !this.view.physicsCtr) {
            return;
        }
        
        // 创建弹珠物理对象
        const ball = this.view.physicsCtr.createBall();
        
        // 计算发射力度（添加随机性避免重叠）
        const randomX = (Math.random() - 0.5) * 0.2;
        const randomY = (Math.random() - 0.5) * 0.1;
        const offsetX = ballIndex * 0.02; // 轻微水平偏移
        
        const launchForce = {
            x: randomX + offsetX,
            y: -launchPower + randomY
        };
        
        // 应用发射力
        PhysicsUtil.applyLinearImpulse(ball, launchForce);
        
        // 设置弹珠用户数据
        const userData = ball.GetUserData() || {};
        userData.ballId = `ball_${Date.now()}_${ballIndex}`;
        userData.multiplier = this.getCurrentMultiplier();
        userData.launchTime = Date.now();
        ball.SetUserData(userData);
    }
    
    /**
     * 播放倍数切换动画
     */
    private playMultiplierSwitchAnimation(): void {
        if (!this.view || !this.view.multiplierDisplay) {
            return;
        }
        
        const display = this.view.multiplierDisplay;
        const currentMultiplier = this.getCurrentMultiplier();
        
        // 缩放动画
        egret.Tween.removeTweens(display);
        egret.Tween.get(display)
            .to({ scaleX: 1.2, scaleY: 1.2 }, 150, egret.Ease.quadOut)
            .to({ scaleX: 1.0, scaleY: 1.0 }, 150, egret.Ease.quadIn)
            .call(() => {
                // 更新显示文本
                if (display.text !== undefined) {
                    display.text = `x${currentMultiplier}`;
                }
            });
    }
    
    /**
     * 通知倍数变更
     */
    private notifyMultiplierChanged(): void {
        const currentMultiplier = this.getCurrentMultiplier();
        
        if (this.config.onMultiplierChanged) {
            this.config.onMultiplierChanged(currentMultiplier);
        }
        
        // 更新视图显示
        if (this.view && this.view.updateMultiplierDisplay) {
            this.view.updateMultiplierDisplay(currentMultiplier);
        }
    }
    
    /**
     * 通知余额变更
     */
    private notifyBalanceChanged(): void {
        const currentBalance = this.getTokenBalance();
        
        if (this.config.onBalanceChanged) {
            this.config.onBalanceChanged(currentBalance);
        }
        
        // 更新视图显示
        if (this.view && this.view.updateTokenDisplay) {
            this.view.updateTokenDisplay(currentBalance);
        }
        
        // 更新可用倍数显示
        if (this.view && this.view.updateAvailableMultipliers) {
            const availableMultipliers = this.getAvailableMultipliers();
            this.view.updateAvailableMultipliers(availableMultipliers);
        }
    }
    
    /**
     * 销毁控制器，清理资源
     */
    public destroy(): void {
        this.view = null;
        // 清理其他资源...
    }
}

/**
 * 创建倍数控制器的工厂函数
 * @param config 配置参数
 * @returns MultiplierController实例
 */
export function createMultiplierController(config: IMultiplierControllerConfig): MultiplierController {
    return new MultiplierController(config);
}
