# Cake Party 弹珠游戏前端技术方案

## 1. 项目概述

### 1.1 需求背景
基于PRD v6.15 - Cake Party中的9.6 Pinball游戏需求，实现一个社交化的弹珠小游戏。用户通过组队合作，使用通关主线关卡获得的代币来玩弹珠游戏，获取分数来制作蛋糕并获得奖励。

### 1.2 核心功能
- 弹珠物理引擎游戏
- 倍数选择机制（x1, x5, x10, x20, x50）
- 分数计算与累积
- 团队协作进度展示
- 蛋糕制作进度管理

### 1.3 整体游戏流程图

```mermaid
flowchart TD
    A[进入Cake Party弹珠游戏] --> B{检查团队状态}
    B -->|未组队| C[显示组队界面]
    B -->|已组队| D[选择蛋糕]

    C --> E[邀请好友/加入团队]
    E --> F{组队成功}
    F -->|失败| C
    F -->|成功| D

    D --> G[进入弹珠游戏界面]
    G --> H[显示代币余额]
    H --> I[自动选择倍数]

    I --> J{代币是否充足}
    J -->|不足| K[提示去主线获取代币]
    J -->|充足| L[用户拉动弹簧]

    K --> M[跳转主线游戏]
    M --> N[获得代币后返回]
    N --> H

    L --> O[释放弹簧发射弹珠]
    O --> P[弹珠在桌面运动]
    P --> Q[撞击计分器]

    Q --> R{撞击次数 < 10}
    R -->|是| S[累积分数]
    R -->|否| T[停用撞击器]

    S --> U[弹珠继续运动]
    T --> U
    U --> V[弹珠进入奖励区域]

    V --> W[计算最终得分]
    W --> X[更新团队进度]
    X --> Y{达成里程碑}

    Y -->|是| Z[播放里程碑特效]
    Y -->|否| AA[继续游戏]

    Z --> BB{蛋糕完成}
    BB -->|否| AA
    BB -->|是| CC[蛋糕完成庆祝]

    CC --> DD{所有蛋糕完成}
    DD -->|否| EE[切换下个蛋糕]
    DD -->|是| FF[获得大奖]

    EE --> D
    AA --> GG{还有代币}
    GG -->|是| I
    GG -->|否| K

    FF --> HH[游戏结束]

    style A fill:#e3f2fd
    style D fill:#e8f5e8
    style O fill:#fff3e0
    style W fill:#f3e5f5
    style FF fill:#ffebee
```

## 2. 技术架构设计

### 2.1 整体架构
参考现有Monster游戏场景管理模式，采用MVC架构：

```
CakePartyPinballScene (场景管理)
├── CakePartyGameManager (游戏管理器)
├── CakePartyPinballController (弹珠控制器)
├── CakePartyPinballView (视图层)
└── CakePartyPinballModel (数据模型)
```

### 2.2 核心模块设计

#### 2.2.1 系统架构流程图

```mermaid
graph TB
    A[CakePartyPinballScene] --> B[CakePartyGameManager]
    B --> C[CakePartyPinballController]
    B --> D[CakePartyScoreManager]
    B --> E[CakePartyTeamManager]

    C --> F[PhysicsController]
    C --> G[LaunchController]
    C --> H[TableController]
    C --> I[MultiplierController]

    F --> J[Box2D World]
    G --> K[Spring System]
    H --> L[Bumpers & Zones]
    I --> M[Token Management]

    D --> N[Score Calculator]
    D --> O[Milestone Tracker]
    E --> P[Team Progress]
    E --> Q[Friend Sync]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

#### 2.2.2 场景管理 (CakePartyPinballScene)
基于 `MonsterGameScene` 的场景管理模式：

```typescript
export class CakePartyPinballScene extends GameBaseScene {
    public gameMgr: CakePartyGameManager;

    protected async init(data: ICakePartySceneData) {
        // 场景初始化
        await this.initGameMgr(data);
        this.initController();
        this.addEventListeners();
    }

    private async initGameMgr(data: ICakePartySceneData) {
        this.gameMgr = new CakePartyGameManager();
        await this.gameMgr.init(data, this);
    }
}
```

#### 2.2.3 游戏管理器 (CakePartyGameManager)
参考 `MonsterGameManager` 架构：

```typescript
export class CakePartyGameManager extends BaseGameManager {
    private pinballController: CakePartyPinballController;
    private scoreManager: CakePartyScoreManager;
    private teamManager: CakePartyTeamManager;

    public async init(data: ICakePartyInitData, parent: IGameView) {
        super.init(data, parent);
        this.initPinballGame();
        this.initScoreSystem();
        this.initTeamSystem();
    }
}
```

#### 2.2.4 弹珠控制器 (CakePartyPinballController)
基于现有 `PinballGameStartController` 和 `PinballGameTableController`：

```typescript
export class CakePartyPinballController {
    private physicsController: PhysicsController;
    private gameStartController: PinballGameStartController;
    private gameTableController: PinballGameTableController;
    private multiplierController: MultiplierController;

    public init() {
        this.initPhysicsWorld();
        this.initGameTable();
        this.initMultiplierSystem();
    }
}
```

## 3. Box2D物理引擎深度调研

### 3.1 Box2D物理引擎基础概念

#### 3.1.1 什么是物理引擎？
物理引擎是一个计算机程序，用于模拟现实世界中的物理现象，如重力、碰撞、摩擦力等。在游戏开发中，物理引擎让虚拟对象的行为更加真实自然。

```mermaid
graph LR
    A[现实世界物理] --> B[数学模型]
    B --> C[计算机算法]
    C --> D[游戏中的物理效果]

    A1[重力] --> B1[F = mg]
    A2[碰撞] --> B2[动量守恒]
    A3[摩擦] --> B3[f = μN]

    B1 --> C1[重力计算]
    B2 --> C2[碰撞检测]
    B3 --> C3[摩擦模拟]

    C1 --> D1[弹珠下落]
    C2 --> D2[弹珠撞击]
    C3 --> D3[弹珠减速]

    style A fill:#e3f2fd
    style D fill:#e8f5e8
```

#### 3.1.2 Box2D引擎特点
Box2D是一个开源的2D物理引擎，专门为游戏开发设计：

**核心优势：**
- **高性能**：使用C++编写，经过高度优化
- **稳定性**：经过数百万用户验证，稳定可靠
- **精确性**：使用定点数运算，避免浮点误差
- **易用性**：API设计简洁，学习成本低
- **跨平台**：支持多种平台和编程语言

**适用场景：**
- 弹珠游戏（如我们的项目）
- 物理解谜游戏
- 平台跳跃游戏
- 赛车游戏的物理模拟

#### 3.1.3 Box2D核心概念图解

```mermaid
graph TD
    A[Box2D物理世界] --> B[刚体 Body]
    A --> C[关节 Joint]
    A --> D[接触 Contact]

    B --> E[静态刚体<br/>Static Body]
    B --> F[动态刚体<br/>Dynamic Body]
    B --> G[运动学刚体<br/>Kinematic Body]

    E --> H[游戏边界<br/>撞击器底座]
    F --> I[弹珠<br/>可移动物体]
    G --> J[移动平台<br/>传送带]

    B --> K[形状 Shape]
    K --> L[圆形 Circle]
    K --> M[矩形 Box]
    K --> N[多边形 Polygon]

    L --> O[弹珠]
    M --> P[撞击器]
    N --> Q[复杂障碍物]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style K fill:#e8f5e8
```

### 3.2 Box2D工作原理深度解析

#### 3.2.1 Box2D物理模拟流程图

```mermaid
flowchart TD
    A[游戏帧开始] --> B[收集用户输入]
    B --> C[应用外力和冲量]
    C --> D[物理世界步进]

    D --> E[广相位碰撞检测]
    E --> F[窄相位碰撞检测]
    F --> G[生成接触点]

    G --> H[约束求解器]
    H --> I[速度约束求解]
    I --> J[位置约束求解]

    J --> K[积分更新]
    K --> L[更新刚体位置]
    L --> M[更新刚体速度]

    M --> N[触发碰撞回调]
    N --> O[清理临时数据]
    O --> P[渲染更新]

    P --> Q[等待下一帧]
    Q --> A

    style A fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#e8f5e8
    style H fill:#f3e5f5
    style N fill:#fce4ec
```

#### 3.2.2 物理引擎的核心算法

**1. 碰撞检测的两阶段算法：**

```mermaid
graph LR
    A[所有物体] --> B[广相位检测<br/>Broad Phase]
    B --> C[可能碰撞对<br/>Potential Pairs]
    C --> D[窄相位检测<br/>Narrow Phase]
    D --> E[实际碰撞对<br/>Actual Contacts]

    B --> F[AABB包围盒<br/>快速排除]
    D --> G[精确形状检测<br/>SAT算法]

    F --> H[O(n log n)复杂度<br/>空间分区优化]
    G --> I[O(1)复杂度<br/>每对物体]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style E fill:#e8f5e8
```

**2. 约束求解的迭代过程：**

```mermaid
graph TD
    A[接触约束] --> B[构建雅可比矩阵]
    B --> C[计算约束力]
    C --> D[应用冲量修正]
    D --> E{收敛检查}

    E -->|未收敛| F[下一次迭代]
    E -->|已收敛| G[约束求解完成]

    F --> C

    G --> H[更新物体状态]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style E fill:#e8f5e8
    style G fill:#f3e5f5
```

### 3.3 现有Box2D架构分析
基于现有代码分析，项目已集成Box2D物理引擎，具备以下特性：

#### 3.2.1 物理世界管理详解

**什么是物理世界？**
物理世界（World）是Box2D中的核心概念，它就像一个虚拟的"宇宙"，包含了所有的物理对象和物理规律。

```mermaid
graph TB
    A[物理世界 World] --> B[重力设置]
    A --> C[时间步进]
    A --> D[碰撞检测]
    A --> E[约束求解]

    B --> F[向下的重力<br/>模拟地球引力]
    C --> G[每秒60次更新<br/>保证流畅性]
    D --> H[检测物体碰撞<br/>触发回调事件]
    E --> I[处理物体约束<br/>保持稳定性]

    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#f3e5f5
    style I fill:#fce4ec
```

**PinBall 游戏中PhysicsController实现：**
```typescript
// 现有PhysicsController核心功能
export class PhysicsController {
    public world: b2World;  // Box2D物理世界实例

    public buildWorld(): void {
        // 创建物理世界，设置重力
        // gravity: 重力加速度，正值向下，负值向上
        this.world = b2World.Create({ x: 0, y: pinballPhysicsConfig.gravity });
        this.createContactListener(); // 碰撞监听
    }

    public updateWorld(): boolean {
        // 物理步进：每帧调用一次，推进物理模拟
        // 时间步长: 1/60秒，保证60FPS的流畅度
        // 速度迭代: 4次，用于计算物体速度
        // 位置迭代: 3次，用于计算物体位置
        this.world.Step(1 / pinballPhysicsConfig.simulateSpeed,
                        { velocityIterations: 4, positionIterations: 3 });
        return false;
    }
}
```

**关键参数解释：**
- **重力 (gravity)**：模拟地球引力，让弹珠自然下落
- **时间步长 (timeStep)**：每次物理更新的时间间隔，通常是1/60秒
- **速度迭代 (velocityIterations)**：计算物体速度的精度，次数越多越精确但越耗性能
- **位置迭代 (positionIterations)**：计算物体位置的精度，影响碰撞的准确性

#### 3.2.2 刚体创建与管理详解

**什么是刚体？**
刚体（Body）是物理世界中的基本对象，代表游戏中的一个物理实体。想象成现实中的物体，比如弹珠、墙壁、撞击器等。

```mermaid
graph LR
    A[刚体类型] --> B[静态刚体<br/>Static Body]
    A --> C[动态刚体<br/>Dynamic Body]
    A --> D[运动学刚体<br/>Kinematic Body]

    B --> E[特点：不会移动<br/>无限质量<br/>不受力影响]
    C --> F[特点：可以移动<br/>有质量<br/>受力影响]
    D --> G[特点：可控制移动<br/>无限质量<br/>不受力影响]

    E --> H[游戏应用：<br/>墙壁、地面<br/>撞击器底座]
    F --> I[游戏应用：<br/>弹珠、可移动物体<br/>掉落的道具]
    G --> J[游戏应用：<br/>移动平台<br/>电梯、传送带]

    style B fill:#ffebee
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

**刚体的物理属性：**

| 属性 | 说明 | 弹珠游戏中的作用 |
|------|------|------------------|
| **密度 (density)** | 物体的密度，影响质量 | 决定弹珠的重量，影响运动惯性 |
| **摩擦力 (friction)** | 表面摩擦系数 | 弹珠与桌面的摩擦，影响滚动速度 |
| **弹性 (restitution)** | 弹性系数，0-1之间 | 弹珠撞击后的反弹程度 |
| **阻尼 (damping)** | 线性和角度阻尼 | 模拟空气阻力，让弹珠逐渐减速 |

**现有弹珠刚体创建实现：**
```typescript
// 弹珠刚体创建（现有实现）
public createBall(): b2Body {
    const params: IPhysicsCircleParams = {
        radius: pinballConfig.ballRadius,        // 弹珠半径
        bodyType: b2BodyType.b2_dynamicBody,    // 动态刚体：可以移动和旋转
        bullet: true,                           // 高速碰撞检测：防止高速穿透
        allowSleep: false,                      // 禁止休眠：保持持续计算
        density: pinballPhysicsConfig.ballDensity,    // 密度：影响质量
        friction: pinballPhysicsConfig.ballFriction,  // 摩擦力：影响滚动
        restitution: 0.2,                       // 弹性系数：较低，避免过度弹跳
        filter: {
            categoryBits: 0x3,                  // 碰撞分类：弹珠类别
            maskBits: 0xffff,                   // 碰撞掩码：可与所有物体碰撞
        },
        userData: { id: 'ball' }                // 用户数据：标识这是弹珠
    };
    return this.createCircle(params);
}
```

**关键参数详解：**
- **bullet: true**：启用连续碰撞检测，防止高速弹珠穿透障碍物
- **allowSleep: false**：禁止休眠优化，确保弹珠始终参与物理计算
- **filter**：碰撞过滤器，控制哪些物体可以相互碰撞

#### 3.2.3 碰撞检测机制详解

**什么是碰撞检测？**
碰撞检测是物理引擎的核心功能，用于检测两个物体是否相撞，并在碰撞时触发相应的处理逻辑。

```mermaid
graph TD
    A[碰撞检测流程] --> B[广相位检测<br/>Broad Phase]
    B --> C[窄相位检测<br/>Narrow Phase]
    C --> D[碰撞响应<br/>Collision Response]

    B --> E[快速筛选<br/>可能碰撞的物体对]
    C --> F[精确计算<br/>碰撞点和法向量]
    D --> G[物理响应<br/>速度、位置调整]
    D --> H[游戏逻辑<br/>触发事件回调]

    E --> I[使用AABB包围盒<br/>快速排除不可能碰撞]
    F --> J[计算接触点<br/>碰撞深度和方向]
    G --> K[应用冲量<br/>改变物体运动状态]
    H --> L[触发游戏事件<br/>播放音效、特效]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
```

**碰撞检测的生命周期：**

| 阶段 | 触发时机 | 用途 |
|------|----------|------|
| **BeginContact** | 两物体开始接触 | 记录碰撞开始，播放音效 |
| **EndContact** | 两物体分离 | 清理碰撞状态 |
| **PreSolve** | 碰撞求解前 | 可以修改碰撞参数或禁用碰撞 |
| **PostSolve** | 碰撞求解后 | 获取碰撞冲量，用于特效强度 |

**现有碰撞监听器实现：**
```typescript
// 现有碰撞监听器实现
private createContactListener(): void {
    const listener = new b2ContactListener();

    // 碰撞开始时触发
    listener.BeginContact = (contact: b2Contact) => {
        const bodyA = contact.GetFixtureA().GetBody();  // 获取碰撞物体A
        const bodyB = contact.GetFixtureB().GetBody();  // 获取碰撞物体B

        // 处理碰撞回调：判断碰撞类型，执行相应逻辑
        this.handleCollision(bodyA, bodyB);
    };

    // 碰撞结束时触发
    listener.EndContact = (contact: b2Contact) => {
        // 清理碰撞状态，停止持续效果
        this.handleCollisionEnd(contact);
    };

    // 设置碰撞监听器到物理世界
    this.world.SetContactListener(listener);
}

// 碰撞处理示例
private handleCollision(bodyA: b2Body, bodyB: b2Body): void {
    const userDataA = bodyA.GetUserData();
    const userDataB = bodyB.GetUserData();

    // 判断是否是弹珠与撞击器的碰撞
    if (this.isBallBumperCollision(userDataA, userDataB)) {
        // 播放撞击音效
        SoundController.playEffect('bumper_hit');

        // 增加分数
        this.addScore(userDataA.multiplier || 1);

        // 播放撞击特效
        this.playHitEffect(bodyA, bodyB);
    }
}
```

**碰撞过滤机制：**
Box2D提供了强大的碰撞过滤功能，可以精确控制哪些物体可以碰撞：

```typescript
// 碰撞分组示例
const COLLISION_GROUPS = {
    BALL: 0x0001,        // 弹珠
    BUMPER: 0x0002,      // 撞击器
    WALL: 0x0004,        // 墙壁
    SENSOR: 0x0008,      // 传感器（奖励区域）
};

// 弹珠只与撞击器、墙壁、传感器碰撞，不与其他弹珠碰撞
const ballFilter = {
    categoryBits: COLLISION_GROUPS.BALL,
    maskBits: COLLISION_GROUPS.BUMPER | COLLISION_GROUPS.WALL | COLLISION_GROUPS.SENSOR
};
```

### 3.3 Box2D性能特性深度分析

#### 3.3.1 Box2D的技术优势

```mermaid
graph LR
    A[Box2D优势] --> B[高精度计算]
    A --> C[稳定性保证]
    A --> D[性能优化]
    A --> E[易用性设计]

    B --> F[连续碰撞检测<br/>CCD技术]
    B --> G[定点数运算<br/>避免浮点误差]

    C --> H[经过验证<br/>数百万用户使用]
    C --> I[数值稳定<br/>长时间运行不发散]

    D --> J[高效算法<br/>优化的数据结构]
    D --> K[内存管理<br/>对象池技术]

    E --> L[简洁API<br/>易于学习使用]
    E --> M[丰富文档<br/>完善的示例]

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

**1. 高精度物理模拟**
- **连续碰撞检测（CCD）**：防止高速物体穿透，特别适合弹珠游戏
- **定点数运算**：避免浮点数累积误差，保证长时间运行的稳定性
- **精确的碰撞响应**：基于冲量的碰撞处理，物理效果真实

**2. 稳定的数值计算**
- **迭代求解器**：使用多次迭代来求解约束，保证数值稳定
- **时间步长控制**：固定时间步长，避免帧率波动影响物理效果
- **约束稳定化**：防止约束违反，保持物理系统的一致性

**3. 丰富的功能支持**
- **多种刚体类型**：静态、动态、运动学刚体满足不同需求
- **灵活的形状系统**：圆形、矩形、多边形、链条等
- **约束系统**：关节、马达、弹簧等复杂机械结构

#### 3.3.2 性能考量与优化策略

**计算复杂度分析：**

| 操作 | 时间复杂度 | 说明 |
|------|------------|------|
| 广相位碰撞检测 | O(n log n) | 使用空间分区算法优化 |
| 窄相位碰撞检测 | O(k) | k为实际碰撞对数量 |
| 约束求解 | O(m × i) | m为约束数量，i为迭代次数 |
| 积分更新 | O(n) | n为刚体数量 |

**内存使用分析：**
- **每个刚体**：约200-400字节（取决于形状复杂度）
- **每个接触点**：约100-200字节
- **物理世界**：基础开销约1-2KB

**性能优化建议：**

```mermaid
graph TD
    A[性能优化策略] --> B[减少计算量]
    A --> C[优化内存使用]
    A --> D[合理设置参数]

    B --> E[限制活跃物体数量]
    B --> F[使用碰撞分组]
    B --> G[启用休眠机制]

    C --> H[对象池管理]
    C --> I[及时清理无用物体]
    C --> J[复用物理形状]

    D --> K[调整迭代次数]
    D --> L[设置合适时间步长]
    D --> M[优化碰撞过滤]

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

**弹珠游戏的性能考量：**
1. **多弹珠同时存在**：需要限制最大弹珠数量，避免性能下降
2. **频繁碰撞**：撞击器会产生大量碰撞事件，需要优化处理
3. **实时响应**：保证60FPS的流畅体验，物理计算不能超过16.67ms

### 3.4 弹珠游戏物理参数调优指南

#### 3.4.1 物理参数的游戏影响

```mermaid
graph LR
    A[物理参数] --> B[游戏体验]

    A1[重力] --> B1[弹珠下落速度<br/>游戏节奏感]
    A2[摩擦力] --> B2[弹珠滚动距离<br/>控制精度]
    A3[弹性] --> B3[撞击反弹效果<br/>视觉冲击力]
    A4[密度] --> B4[弹珠惯性<br/>真实感]

    B1 --> C1[太快：难以观察<br/>太慢：缺乏刺激]
    B2 --> C2[太高：难以停止<br/>太低：缺乏滑动]
    B3 --> C3[太高：过度弹跳<br/>太低：缺乏活力]
    B4 --> C4[太重：反应迟钝<br/>太轻：不够真实]

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style C3 fill:#fff3e0
    style C4 fill:#fff3e0
```

#### 3.4.2 关键参数配置详解

**完整的物理配置接口：**
```typescript
interface IPinballPhysicsConfig {
    // 物理世界参数
    gravity: 9.8,                    // 重力加速度：控制弹珠下落速度
    simulateSpeed: 60,               // 模拟频率：60Hz保证流畅性
    worldScale: 30,                  // 世界缩放：像素到物理单位的转换

    // 弹珠参数
    ballRadius: 12,                  // 弹珠半径：影响碰撞检测范围
    ballDensity: 1.0,               // 弹珠密度：影响质量和惯性
    ballFriction: 0.1,              // 弹珠摩擦系数：影响滚动阻力
    ballRestitution: 0.6,           // 弹珠弹性系数：影响反弹程度
    ballLinearDamping: 0.1,         // 线性阻尼：模拟空气阻力
    ballAngularDamping: 0.1,        // 角度阻尼：模拟旋转阻力

    // 弹射参数
    springMaxPower: 15.0,           // 弹簧最大力度：控制发射速度上限
    springMinPower: 5.0,            // 弹簧最小力度：保证最小发射力
    launchAngleVariance: 0.1,       // 发射角度随机性：增加游戏变化

    // 撞击器参数
    bumperRestitution: 1.2,         // 撞击器弹性：>1产生加速效果
    bumperRadius: 36,               // 撞击器半径：影响碰撞范围
    bumperFriction: 0.2,            // 撞击器摩擦：影响弹珠轨迹

    // 性能参数
    velocityIterations: 4,          // 速度迭代次数：影响计算精度
    positionIterations: 3,          // 位置迭代次数：影响稳定性
    maxActiveBodies: 50,            // 最大活跃刚体数：性能控制
}
```

#### 3.4.3 参数调优的实践经验

**重力调优：**
```typescript
// 不同重力值的游戏体验
const GRAVITY_PRESETS = {
    SLOW_MOTION: 5.0,      // 慢动作效果，适合新手
    NORMAL: 9.8,           // 接近真实重力
    FAST_PACED: 15.0,      // 快节奏游戏
    ARCADE: 20.0,          // 街机风格，刺激感强
};
```

**弹性系数的巧妙运用：**
```typescript
// 不同物体的弹性设置
const RESTITUTION_SETTINGS = {
    BALL: 0.6,             // 弹珠：适中弹性，避免过度弹跳
    BUMPER: 1.2,           // 撞击器：超弹性，产生加速效果
    WALL: 0.3,             // 墙壁：低弹性，快速消耗动能
    RUBBER: 1.0,           // 橡胶：完全弹性，保持能量
};
```

**摩擦力的细致调节：**
```typescript
// 不同表面的摩擦设置
const FRICTION_SETTINGS = {
    BALL_ON_TABLE: 0.1,    // 弹珠在桌面：轻微摩擦，保持滑动
    BALL_ON_RUBBER: 0.3,   // 弹珠在橡胶：较高摩擦，改变方向
    BALL_ON_METAL: 0.05,   // 弹珠在金属：极低摩擦，快速滑过
};
```

#### 3.4.5 物理材质设计系统

**材质系统的设计理念：**
不同的游戏元素需要不同的物理属性来营造真实的游戏体验。就像现实中的材料有不同的特性一样。

```mermaid
graph TD
    A[物理材质系统] --> B[弹珠材质]
    A --> C[撞击器材质]
    A --> D[边界材质]
    A --> E[特殊材质]

    B --> F[钢珠特性<br/>高密度、低摩擦<br/>适中弹性]
    C --> G[弹性橡胶<br/>超高弹性<br/>产生加速效果]
    D --> H[木质边框<br/>高摩擦、低弹性<br/>快速消耗能量]
    E --> I[传感器区域<br/>无物理碰撞<br/>仅触发事件]

    F --> J[真实的滚动感]
    G --> K[刺激的撞击感]
    H --> L[稳定的边界]
    I --> M[精确的检测]

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

**完整的材质配置系统：**
```typescript
// 物理材质配置接口
interface IPhysicsMaterial {
    name: string;           // 材质名称
    density: number;        // 密度（0表示静态物体）
    friction: number;       // 摩擦系数（0-1）
    restitution: number;    // 弹性系数（0-2，>1表示增能）
    isSensor?: boolean;     // 是否为传感器
    category?: number;      // 碰撞分类
    mask?: number;          // 碰撞掩码
    sound?: string;         // 碰撞音效
    effect?: string;        // 碰撞特效
}

// 预定义的物理材质库
const PHYSICS_MATERIALS: Record<string, IPhysicsMaterial> = {
    // 弹珠材质：模拟钢珠
    BALL: {
        name: 'steel_ball',
        density: 7.8,              // 钢的密度
        friction: 0.1,             // 光滑表面，低摩擦
        restitution: 0.6,          // 适中弹性，避免过度弹跳
        category: 0x0001,          // 弹珠碰撞分类
        mask: 0xFFFE,              // 与除自己外所有物体碰撞
        sound: 'ball_roll',        // 滚动音效
    },

    // 撞击器材质：高弹性橡胶
    BUMPER: {
        name: 'elastic_rubber',
        density: 0,                // 静态物体
        friction: 0.2,             // 轻微摩擦，改变弹珠方向
        restitution: 1.2,          // 超弹性，产生加速效果
        category: 0x0002,          // 撞击器碰撞分类
        mask: 0x0001,              // 只与弹珠碰撞
        sound: 'bumper_hit',       // 撞击音效
        effect: 'bumper_flash',    // 撞击特效
    },

    // 边界墙壁：木质材料
    WALL: {
        name: 'wooden_wall',
        density: 0,                // 静态物体
        friction: 1.0,             // 高摩擦，快速减速
        restitution: 0.3,          // 低弹性，吸收能量
        category: 0x0004,          // 墙壁碰撞分类
        mask: 0x0001,              // 只与弹珠碰撞
        sound: 'wall_hit',         // 撞墙音效
    },

    // 橡胶挡板：完全弹性
    RUBBER: {
        name: 'pure_rubber',
        density: 0,                // 静态物体
        friction: 0.2,             // 适中摩擦
        restitution: 1.0,          // 完全弹性，保持能量
        category: 0x0008,          // 橡胶碰撞分类
        mask: 0x0001,              // 只与弹珠碰撞
        sound: 'rubber_bounce',    // 弹跳音效
        effect: 'rubber_ripple',   // 波纹特效
    },

    // 奖励区域传感器：无物理碰撞
    BONUS_SENSOR: {
        name: 'bonus_zone',
        density: 0,                // 静态物体
        friction: 0,               // 无摩擦
        restitution: 0,            // 无弹性
        isSensor: true,            // 传感器模式
        category: 0x0010,          // 传感器碰撞分类
        mask: 0x0001,              // 只检测弹珠
        sound: 'bonus_collect',    // 收集音效
        effect: 'bonus_sparkle',   // 闪光特效
    }
};

// 材质应用示例
export class MaterialManager {
    // 应用材质到刚体
    public static applyMaterial(body: b2Body, materialName: string): void {
        const material = PHYSICS_MATERIALS[materialName];
        if (!material) return;

        // 设置物理属性
        const fixture = body.GetFixtureList();
        if (fixture) {
            fixture.SetDensity(material.density);
            fixture.SetFriction(material.friction);
            fixture.SetRestitution(material.restitution);
            fixture.SetSensor(material.isSensor || false);
        }

        // 设置碰撞过滤
        if (material.category && material.mask) {
            const filter = fixture.GetFilterData();
            filter.categoryBits = material.category;
            filter.maskBits = material.mask;
            fixture.SetFilterData(filter);
        }

        // 保存材质信息到用户数据
        const userData = body.GetUserData() || {};
        userData.material = material;
        body.SetUserData(userData);
    }

    // 获取碰撞音效
    public static getCollisionSound(bodyA: b2Body, bodyB: b2Body): string {
        const materialA = bodyA.GetUserData()?.material;
        const materialB = bodyB.GetUserData()?.material;

        // 优先返回特殊材质的音效
        return materialB?.sound || materialA?.sound || 'default_hit';
    }
}
```

### 3.5 Box2D在弹珠游戏中的实际应用

#### 3.5.1 弹珠游戏物理场景构建

```mermaid
graph TB
    A[弹珠游戏桌面] --> B[静态边界]
    A --> C[动态弹珠]
    A --> D[撞击器系统]
    A --> E[传感器区域]

    B --> F[左右边界墙]
    B --> G[底部收集区]
    B --> H[弹射台]

    C --> I[钢珠物理属性]
    C --> J[重力影响]
    C --> K[碰撞响应]

    D --> L[弹性撞击器]
    D --> M[橡胶挡板]
    D --> N[旋转障碍]

    E --> O[分数区域]
    E --> P[倍数区域]
    E --> Q[特殊效果区]

    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#f3e5f5
```

#### 3.5.2 Box2D常见问题与解决方案

**问题1：高速弹珠穿透障碍物**
- **原因**：离散碰撞检测在高速运动时会错过碰撞
- **解决**：启用连续碰撞检测（CCD）
```typescript
ballBody.SetBullet(true);  // 启用CCD
```

**问题2：物理计算不稳定**
- **原因**：时间步长不固定或过大
- **解决**：使用固定时间步长
```typescript
const FIXED_TIME_STEP = 1/60;  // 固定60FPS
```

**问题3：弹珠卡在角落**
- **原因**：摩擦力过大或几何形状问题
- **解决**：添加防卡机制和合理的物理参数

## 4. 弹珠游戏核心实现

### 4.1 弹射系统实现

#### 4.1.1 弹珠发射准备流程图

```mermaid
flowchart TD
    A[用户进入弹珠游戏] --> B[显示当前小球余额]
    B --> C[用户选择发射倍数]

    C --> D{检查小球余额}
    D -->|余额不足| E[显示余额不足提示<br/>引导获取更多小球]
    D -->|余额充足| F[显示可选倍数选项]

    F --> G[用户选择倍数<br/>1x, 3x, 5x, 10x等]
    G --> H[更新UI显示选中倍数]
    H --> I[用户点击发射按钮]

    I --> J[发起扣除小球请求]
    J --> K[显示Loading状态<br/>棋盘锁定不可操作]

    K --> L{服务器响应}
    L -->|扣除失败| M[显示错误提示<br/>解锁棋盘]
    L -->|扣除成功| N[更新本地余额<br/>准备发射]

    E --> O[跳转获取小球页面]
    M --> F
    N --> P[进入发射阶段]

    style A fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#ffebee
    style K fill:#f3e5f5
    style N fill:#e8f5e8
```

#### 4.1.2 弹珠发射执行流程图

```mermaid
flowchart TD
    A[发射准备完成] --> B[用户拉动弹簧]
    B --> C[更新弹簧视觉效果<br/>显示蓄力程度]

    C --> D[用户释放弹簧]
    D --> E[计算随机初始速度<br/>添加轨迹变化]

    E --> F[根据选择倍数设置分数基数]
    F --> G{倍数设置}

    G -->|1x| H[分数基数 = 1]
    G -->|3x| I[分数基数 = 3]
    G -->|5x| J[分数基数 = 5]
    G -->|10x| K[分数基数 = 10]

    H --> L[弹珠发射进入物理世界]
    I --> L
    J --> L
    K --> L

    L --> M[轨道挡板落下<br/>防止小球回流]
    M --> N[开始轨迹追踪]
    N --> O[等待碰撞事件]

    style A fill:#e3f2fd
    style D fill:#fff3e0
    style F fill:#e8f5e8
    style L fill:#f3e5f5
    style M fill:#fce4ec
```

#### 4.1.2 弹簧机制实现
基于现有 `PinballGameStartController` 的弹射实现：

```typescript
export class CakePartyLaunchController {
    private readonly SPRING_MIN_SCALE = 0.6;
    private springState = EPinballSpringState.READY;
    private currentMultiplier = 1;

    // 拉动弹簧
    public pullSpring(power: number): void {
        if (this.springState !== EPinballSpringState.READY) return;

        const scaleY = 1 - (1 - this.SPRING_MIN_SCALE) * power;
        this.spring.scaleY = scaleY;
        this.updateSpringVisual(scaleY);
        this.updateBallPosition(scaleY);
        this.springState = EPinballSpringState.PULLING;
    }

    // 释放弹簧，发射弹珠
    public releaseSpring(): void {
        if (this.springState !== EPinballSpringState.PULLING) return;

        const springPower = 1 - this.spring.scaleY;
        this.springState = EPinballSpringState.RELEASING;

        // 根据倍数创建多个弹珠
        this.launchMultipleBalls(springPower, this.currentMultiplier);

        // 弹簧回弹动画
        this.playSpringReleaseAnimation();
    }

    // 多倍数弹珠发射
    private launchMultipleBalls(power: number, multiplier: number): void {
        for (let i = 0; i < multiplier; i++) {
            setTimeout(() => {
                const ball = this.createBall();
                const launchForce = this.calculateLaunchForce(power, i);
                PhysicsUtil.applyLinearImpulse(ball, launchForce);
            }, i * 100); // 间隔100ms发射
        }
    }

    // 计算发射力度（添加随机性）
    private calculateLaunchForce(power: number, ballIndex: number): XY {
        const baseForce = power * pinballPhysicsConfig.springMaxPower;
        const randomX = randomRange(-0.1, 0.1);
        const randomY = randomRange(-0.05, 0.05);

        return {
            x: randomX + (ballIndex * 0.02), // 轻微偏移避免重叠
            y: -baseForce + randomY
        };
    }
}
```

#### 4.1.3 小球余额管理系统

```typescript
export class BallBalanceManager {
    private currentBalance = 0;
    private availableMultipliers: number[] = [];
    private isLoading = false;

    // 初始化，获取当前小球余额
    public async initialize(): Promise<void> {
        try {
            const response = await this.fetchBallBalance();
            this.currentBalance = response.balance;
            this.updateAvailableMultipliers();
            this.updateUI();
        } catch (error) {
            console.error('Failed to fetch ball balance:', error);
            this.showErrorMessage('获取小球余额失败');
        }
    }

    // 更新可选倍数选项
    private updateAvailableMultipliers(): void {
        const allMultipliers = [1, 3, 5, 10, 20, 50];
        this.availableMultipliers = allMultipliers.filter(
            multiplier => multiplier <= this.currentBalance
        );

        // 至少保证有1倍选项（如果余额大于0）
        if (this.currentBalance > 0 && this.availableMultipliers.length === 0) {
            this.availableMultipliers = [1];
        }
    }

    // 检查是否可以发射指定倍数
    public canLaunch(multiplier: number): boolean {
        return this.currentBalance >= multiplier && !this.isLoading;
    }

    // 发射前扣除小球（服务器验证）
    public async consumeBalls(multiplier: number): Promise<boolean> {
        if (!this.canLaunch(multiplier)) {
            this.showInsufficientBalanceMessage();
            return false;
        }

        this.isLoading = true;
        this.lockGameBoard(); // 锁定棋盘操作

        try {
            const response = await this.requestConsumeBalls(multiplier);

            if (response.success) {
                // 扣除成功，更新本地余额
                this.currentBalance = response.newBalance;
                this.updateAvailableMultipliers();
                this.updateUI();

                SmartEvent.dispatchEventWith(ECakePartyEventType.BALLS_CONSUMED, {
                    consumed: multiplier,
                    remaining: this.currentBalance
                });

                return true;
            } else {
                // 扣除失败，显示错误信息
                this.showErrorMessage(response.errorMessage || '扣除小球失败');
                return false;
            }
        } catch (error) {
            console.error('Failed to consume balls:', error);
            this.showErrorMessage('网络错误，请重试');
            return false;
        } finally {
            this.isLoading = false;
            this.unlockGameBoard(); // 解锁棋盘操作
        }
    }

    // 服务器API调用
    private async fetchBallBalance(): Promise<IBallBalanceResponse> {
        const response = await fetch('/api/pinball/balance', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${this.getAuthToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    private async requestConsumeBalls(amount: number): Promise<IConsumeBallsResponse> {
        const response = await fetch('/api/pinball/consume', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.getAuthToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                amount,
                gameId: this.getCurrentGameId(),
                timestamp: Date.now()
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    // UI更新方法
    private updateUI(): void {
        SmartEvent.dispatchEventWith(ECakePartyEventType.BALANCE_UPDATED, {
            balance: this.currentBalance,
            availableMultipliers: this.availableMultipliers
        });
    }

    private lockGameBoard(): void {
        SmartEvent.dispatchEventWith(ECakePartyEventType.GAME_BOARD_LOCKED, {
            reason: 'consuming_balls'
        });
    }

    private unlockGameBoard(): void {
        SmartEvent.dispatchEventWith(ECakePartyEventType.GAME_BOARD_UNLOCKED);
    }

    private showInsufficientBalanceMessage(): void {
        SmartEvent.dispatchEventWith(ECakePartyEventType.SHOW_MESSAGE, {
            type: 'warning',
            title: '小球不足',
            message: '当前小球数量不足，请先获取更多小球',
            actions: [{
                text: '获取小球',
                callback: () => this.navigateToGetBalls()
            }]
        });
    }

    private showErrorMessage(message: string): void {
        SmartEvent.dispatchEventWith(ECakePartyEventType.SHOW_MESSAGE, {
            type: 'error',
            title: '操作失败',
            message,
            actions: [{
                text: '确定',
                callback: () => {}
            }]
        });
    }

    // 获取器方法
    public getCurrentBalance(): number {
        return this.currentBalance;
    }

    public getAvailableMultipliers(): number[] {
        return [...this.availableMultipliers];
    }

    public isOperationInProgress(): boolean {
        return this.isLoading;
    }
}

// 接口定义
interface IBallBalanceResponse {
    balance: number;
    lastUpdated: number;
}

interface IConsumeBallsResponse {
    success: boolean;
    newBalance: number;
    errorMessage?: string;
    transactionId?: string;
}
```

#### 4.1.4 轨道挡板系统实现

```typescript
export class TrackBarrierController {
    private barriers: Map<string, ITrackBarrier> = new Map();
    private isBarrierActive = false;

    // 初始化轨道挡板
    public initializeBarriers(): void {
        // 创建主轨道挡板（防止小球回流到发射区）
        const mainBarrier = this.createBarrier({
            id: 'main_track_barrier',
            position: { x: 400, y: 600 },
            width: 80,
            height: 20,
            isActive: false // 初始状态为关闭
        });

        this.barriers.set('main_track_barrier', mainBarrier);

        // 创建侧轨道挡板（可选，用于引导小球路径）
        const sideBarriers = [
            { id: 'left_guide', position: { x: 200, y: 500 } },
            { id: 'right_guide', position: { x: 600, y: 500 } }
        ];

        sideBarriers.forEach(config => {
            const barrier = this.createBarrier({
                ...config,
                width: 60,
                height: 15,
                isActive: false
            });
            this.barriers.set(config.id, barrier);
        });
    }

    // 小球发射后激活挡板
    public activateBarriersAfterLaunch(): void {
        if (this.isBarrierActive) return;

        // 延迟激活，给小球足够时间通过
        setTimeout(() => {
            this.activateBarrier('main_track_barrier');
            this.playBarrierActivateEffect();
            this.isBarrierActive = true;

            SmartEvent.dispatchEventWith(ECakePartyEventType.TRACK_BARRIERS_ACTIVATED);
        }, 500); // 500ms延迟
    }

    // 游戏结束后重置挡板
    public resetBarriersAfterGameEnd(): void {
        if (!this.isBarrierActive) return;

        // 等待小球完全结算后再重置
        setTimeout(() => {
            this.deactivateAllBarriers();
            this.playBarrierDeactivateEffect();
            this.isBarrierActive = false;

            SmartEvent.dispatchEventWith(ECakePartyEventType.TRACK_BARRIERS_RESET);
        }, 1000); // 1秒延迟，确保结算完成
    }

    // 创建物理挡板
    private createBarrier(config: IBarrierConfig): ITrackBarrier {
        // 创建物理刚体（初始为传感器状态，不产生碰撞）
        const body = this.physicsCtr.createBox({
            width: config.width,
            height: config.height,
            center: config.position,
            bodyType: b2BodyType.b2_staticBody,
            isSensor: true, // 初始为传感器，不阻挡小球
            userData: {
                id: config.id,
                type: 'track_barrier',
                isActive: config.isActive
            }
        });

        // 创建视觉表现
        const visual = this.createBarrierVisual(config);
        visual.visible = config.isActive;

        return {
            id: config.id,
            body,
            visual,
            isActive: config.isActive,
            config
        };
    }

    // 激活挡板
    private activateBarrier(barrierId: string): void {
        const barrier = this.barriers.get(barrierId);
        if (!barrier || barrier.isActive) return;

        // 将传感器改为实体碰撞
        const fixture = barrier.body.GetFixtureList();
        if (fixture) {
            fixture.SetSensor(false);
        }

        // 更新用户数据
        const userData = barrier.body.GetUserData();
        userData.isActive = true;
        barrier.body.SetUserData(userData);

        // 显示视觉效果
        barrier.visual.visible = true;
        barrier.isActive = true;

        // 播放激活动画
        this.playBarrierActivateAnimation(barrier);
    }

    // 停用所有挡板
    private deactivateAllBarriers(): void {
        this.barriers.forEach(barrier => {
            if (barrier.isActive) {
                // 改回传感器状态
                const fixture = barrier.body.GetFixtureList();
                if (fixture) {
                    fixture.SetSensor(true);
                }

                // 隐藏视觉效果
                barrier.visual.visible = false;
                barrier.isActive = false;

                // 播放停用动画
                this.playBarrierDeactivateAnimation(barrier);
            }
        });
    }

    // 创建挡板视觉效果
    private createBarrierVisual(config: IBarrierConfig): egret.DisplayObject {
        const container = new egret.DisplayObjectContainer();

        // 主体
        const body = new egret.Shape();
        body.graphics.beginFill(0x4CAF50, 0.8);
        body.graphics.drawRoundRect(0, 0, config.width, config.height, 5, 5);
        body.graphics.endFill();

        // 边框
        const border = new egret.Shape();
        border.graphics.lineStyle(2, 0x2E7D32);
        border.graphics.drawRoundRect(0, 0, config.width, config.height, 5, 5);

        // 发光效果
        const glow = new egret.Shape();
        glow.graphics.beginFill(0x81C784, 0.3);
        glow.graphics.drawRoundRect(-2, -2, config.width + 4, config.height + 4, 7, 7);
        glow.graphics.endFill();

        container.addChild(glow);
        container.addChild(body);
        container.addChild(border);

        // 设置位置
        container.x = config.position.x - config.width / 2;
        container.y = config.position.y - config.height / 2;

        return container;
    }

    // 播放激活动画
    private playBarrierActivateAnimation(barrier: ITrackBarrier): void {
        const visual = barrier.visual;
        visual.scaleY = 0;
        visual.alpha = 0;

        egret.Tween.get(visual)
            .to({ scaleY: 1, alpha: 1 }, 300, egret.Ease.backOut)
            .call(() => {
                // 播放激活音效
                SoundController.playEffect('barrier_activate');
            });
    }

    // 播放停用动画
    private playBarrierDeactivateAnimation(barrier: ITrackBarrier): void {
        const visual = barrier.visual;

        egret.Tween.get(visual)
            .to({ scaleY: 0, alpha: 0 }, 200, egret.Ease.quadIn)
            .call(() => {
                // 播放停用音效
                SoundController.playEffect('barrier_deactivate');
            });
    }

    // 播放整体激活特效
    private playBarrierActivateEffect(): void {
        // 屏幕震动效果
        this.gameView.playScreenShake(100, 5);

        // 粒子特效
        this.effectManager.playEffect('barrier_activation_particles', { x: 400, y: 600 });

        // 音效
        SoundController.playEffect('barriers_activated');
    }

    // 播放整体停用特效
    private playBarrierDeactivateEffect(): void {
        // 轻微闪光效果
        this.effectManager.playEffect('barrier_deactivation_flash');

        // 音效
        SoundController.playEffect('barriers_reset');
    }
}

// 接口定义
interface IBarrierConfig {
    id: string;
    position: XY;
    width: number;
    height: number;
    isActive: boolean;
}

interface ITrackBarrier {
    id: string;
    body: b2Body;
    visual: egret.DisplayObject;
    isActive: boolean;
    config: IBarrierConfig;
}
```

### 4.2 撞击器系统实现

#### 4.2.1 橡胶体碰撞处理流程图

```mermaid
flowchart TD
    A[弹珠与橡胶体碰撞] --> B{橡胶体是否有弹性}
    B -->|无弹性| C[普通碰撞<br/>不增加分数]
    B -->|有弹性| D[检查该球碰撞次数]

    D --> E{碰撞次数 < 10}
    E -->|否| F[橡胶体失去弹性<br/>不再计分]
    E -->|是| G[记录碰撞次数+1]

    G --> H[计算本次得分]
    H --> I{是否第10次碰撞}

    I -->|是| J[第10次特殊计分<br/>分数 × 10倍]
    I -->|否| K[普通计分<br/>分数 = 倍数基数]

    J --> L[累加到总分]
    K --> L

    L --> M[播放碰撞特效]
    M --> N[播放音效]
    N --> O[更新分数显示]

    O --> P{检查是否达到10次}
    P -->|是| Q[橡胶体变为无弹性状态]
    P -->|否| R[继续游戏]

    F --> S[弹珠继续运动]
    Q --> S
    R --> S
    C --> S

    style A fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#ffebee
    style I fill:#f3e5f5
    style J fill:#fce4ec
```

#### 4.2.2 计分撞击器实现
```typescript
export class ScoringBumperController {
    private bumpers: Map<string, IScoringBumper> = new Map();
    private readonly MAX_HITS_PER_BALL = 10; // PRD需求：最多10次撞击

    // 创建计分撞击器
    public createScoringBumper(config: IBumperConfig, index: number): void {
        const bumper = this.physicsCtr.createCircle({
            radius: config.radius,
            center: config.position,
            bodyType: b2BodyType.b2_staticBody,
            restitution: 1.2, // 弹性系数>1，产生加速效果
            userData: {
                id: `scoring_bumper_${index}`,
                callback: this.onBumperHit.bind(this),
                bumperIndex: index
            }
        });

        this.bumpers.set(`bumper_${index}`, {
            body: bumper,
            hitCount: 0,
            isActive: true,
            config
        });
    }

    // 撞击器碰撞处理
    private onBumperHit(bumperIndex: number, ball: b2Body): void {
        const bumperKey = `bumper_${bumperIndex}`;
        const bumper = this.bumpers.get(bumperKey);

        if (!bumper || !bumper.isActive) return;

        // 检查该球的撞击次数
        const ballHits = this.getBallHitCount(ball);
        if (ballHits >= this.MAX_HITS_PER_BALL) {
            this.deactivateBumpers(ball);
            return;
        }

        // 记录撞击
        this.recordBumperHit(ball, bumperIndex);

        // 播放撞击效果
        this.playBumperHitEffect(bumperIndex);

        // 分发撞击事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.BUMPER_HIT, {
            bumperIndex,
            ballId: ball.GetUserData().ballId,
            hitCount: ballHits + 1
        });
    }

    // 停用撞击器（达到最大撞击次数）
    private deactivateBumpers(ball: b2Body): void {
        const ballId = ball.GetUserData().ballId;

        this.bumpers.forEach((bumper, key) => {
            if (bumper.isActive) {
                bumper.isActive = false;
                this.playBumperDeactivateEffect(key);
            }
        });

        SmartEvent.dispatchEventWith(ECakePartyEventType.BUMPERS_DEACTIVATED, { ballId });
    }
}
```

#### 4.2.2 奖励区域系统
```typescript
export class BonusZoneController {
    private bonusZones: IBonusZone[] = [];

    // 创建奖励区域
    public createBonusZones(configs: IBonusZoneConfig[]): void {
        configs.forEach((config, index) => {
            const sensor = this.physicsCtr.createBox({
                width: config.width,
                height: config.height,
                center: config.position,
                isSensor: true, // 传感器，不产生物理碰撞
                userData: {
                    id: `bonus_zone_${index}`,
                    callback: this.onBallEnterBonusZone.bind(this),
                    zoneIndex: index,
                    multiplier: config.multiplier
                }
            });

            this.bonusZones.push({
                body: sensor,
                multiplier: config.multiplier,
                index,
                config
            });
        });
    }

    // 弹珠进入奖励区域
    private onBallEnterBonusZone(zoneIndex: number, ball: b2Body): void {
        const zone = this.bonusZones[zoneIndex];
        if (!zone) return;

        const ballData = ball.GetUserData();

        // 计算该球的最终得分
        const finalScore = this.calculateFinalScore(ballData, zone.multiplier);

        // 播放奖励区域效果
        this.playBonusZoneEffect(zoneIndex, finalScore);

        // 分发得分事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.BALL_SCORED, {
            ballId: ballData.ballId,
            zoneIndex,
            zoneMultiplier: zone.multiplier,
            finalScore
        });

        // 移除弹珠
        this.removeBall(ball);
    }
}
```

### 4.3 分数计算系统

#### 4.3.1 最终分数计算流程图

```mermaid
flowchart TD
    A[弹珠进入感应区域] --> B[获取区域倍数<br/>x2, x3, x5等]
    B --> C[获取弹珠碰撞记录]

    C --> D[计算基础分数]
    D --> E{检查碰撞次数}

    E -->|< 10次| F[普通计分公式<br/>倍数基数 × 碰撞次数]
    E -->|= 10次| G[特殊计分公式<br/>倍数基数 × 9 + 倍数基数 × 10]

    F --> H[应用感应区域倍数]
    G --> H

    H --> I[计算最终得分<br/>基础分数 × 区域倍数]
    I --> J[更新个人总分]
    J --> K[累积团队进度]

    K --> L{检查里程碑}
    L -->|达成里程碑| M[触发里程碑奖励]
    L -->|未达成| N[更新进度显示]

    M --> O[播放里程碑特效]
    O --> P[显示奖励内容]
    P --> Q{检查蛋糕完成}

    Q -->|蛋糕完成| R[蛋糕完成庆祝]
    Q -->|未完成| S[继续制作蛋糕]

    R --> T{检查所有蛋糕}
    T -->|全部完成| U[获得大奖]
    T -->|未全部完成| V[切换下个蛋糕]

    N --> S
    V --> S
    U --> W[游戏完成]

    style A fill:#e3f2fd
    style E fill:#fff3e0
    style G fill:#fce4ec
    style I fill:#e8f5e8
    style M fill:#f3e5f5
```

#### 4.3.2 分数计算公式详解

**基础分数计算规则：**

```mermaid
graph LR
    A[用户选择倍数] --> B[碰撞橡胶体]
    B --> C[分数累积]

    A1[选择3个球] --> B1[碰撞6次]
    A2[选择5个球] --> B2[碰撞10次]

    B1 --> C1[3 × 6 = 18分]
    B2 --> C2[5 × 9 + 5 × 10 = 95分]

    C1 --> D1[落入x3区域]
    C2 --> D2[落入x2区域]

    D1 --> E1[最终得分: 18 × 3 = 54分]
    D2 --> E2[最终得分: 95 × 2 = 190分]

    style A fill:#e3f2fd
    style C fill:#e8f5e8
    style E1 fill:#fff3e0
    style E2 fill:#f3e5f5
```

**计算公式总结：**
1. **普通碰撞（< 10次）**：`最终得分 = 倍数基数 × 碰撞次数 × 区域倍数`
2. **满碰撞（= 10次）**：`最终得分 = (倍数基数 × 9 + 倍数基数 × 10) × 区域倍数`
3. **第10次碰撞特殊奖励**：第10次碰撞得分为基数的10倍

#### 4.3.3 分数计算系统实现

```typescript
export class CakePartyScoreCalculator {
    private ballScores: Map<string, IBallScoreRecord> = new Map();
    private rubberBodies: Map<string, IRubberBodyState> = new Map();

    // 小球发射时设置倍数基数
    public initializeBall(ballId: string, multiplierBase: number): void {
        this.ballScores.set(ballId, {
            ballId,
            multiplierBase,        // 用户选择的倍数（1, 3, 5, 10等）
            rubberHits: 0,         // 橡胶体碰撞次数
            baseScore: 0,          // 基础分数
            finalScore: 0,         // 最终分数
            isCompleted: false     // 是否已完成计分
        });
    }

    // 橡胶体碰撞处理
    public handleRubberCollision(ballId: string, rubberId: string): number {
        const ballScore = this.getBallScore(ballId);
        const rubberState = this.getRubberState(rubberId);

        // 检查橡胶体是否还有弹性
        if (!rubberState.hasElasticity) {
            return 0; // 无弹性，不计分
        }

        // 检查该球是否已达到10次碰撞上限
        if (ballScore.rubberHits >= 10) {
            return 0; // 已达上限，不再计分
        }

        // 记录碰撞
        ballScore.rubberHits++;

        // 计算本次得分
        let currentScore = 0;
        if (ballScore.rubberHits === 10) {
            // 第10次碰撞：特殊奖励，分数×10
            currentScore = ballScore.multiplierBase * 10;

            // 橡胶体失去弹性
            rubberState.hasElasticity = false;
            this.playRubberDeactivateEffect(rubberId);
        } else {
            // 普通碰撞：基础分数
            currentScore = ballScore.multiplierBase;
        }

        // 累加到基础分数
        ballScore.baseScore += currentScore;

        // 更新状态
        this.ballScores.set(ballId, ballScore);
        this.rubberBodies.set(rubberId, rubberState);

        // 播放碰撞效果
        this.playRubberHitEffect(rubberId, currentScore, ballScore.rubberHits);

        return currentScore;
    }

    // 感应区域最终计分
    public calculateFinalScore(ballId: string, sensorMultiplier: number): number {
        const ballScore = this.getBallScore(ballId);

        if (ballScore.isCompleted) {
            return ballScore.finalScore; // 已经计算过，直接返回
        }

        // 应用感应区域倍数
        ballScore.finalScore = ballScore.baseScore * sensorMultiplier;
        ballScore.isCompleted = true;

        // 更新记录
        this.ballScores.set(ballId, ballScore);

        // 播放得分特效
        this.playFinalScoreEffect(ballScore.finalScore, sensorMultiplier);

        return ballScore.finalScore;
    }

    // 获取弹珠得分记录
    private getBallScore(ballId: string): IBallScoreRecord {
        const score = this.ballScores.get(ballId);
        if (!score) {
            throw new Error(`Ball score record not found for ball: ${ballId}`);
        }
        return score;
    }

    // 获取橡胶体状态
    private getRubberState(rubberId: string): IRubberBodyState {
        if (!this.rubberBodies.has(rubberId)) {
            this.rubberBodies.set(rubberId, {
                rubberId,
                hasElasticity: true,
                totalHits: 0
            });
        }
        return this.rubberBodies.get(rubberId);
    }

    // 播放橡胶体碰撞特效
    private playRubberHitEffect(rubberId: string, score: number, hitCount: number): void {
        // 播放碰撞音效
        SoundController.playEffect('rubber_hit');

        // 显示分数飞字
        this.showScorePopup(score, this.getRubberPosition(rubberId));

        // 特殊效果：第10次碰撞
        if (hitCount === 10) {
            this.showSpecialEffect('max_hits_bonus', rubberId);
            SoundController.playEffect('max_hits_achieved');
        }
    }

    // 播放最终得分特效
    private playFinalScoreEffect(finalScore: number, multiplier: number): void {
        SoundController.playEffect('final_score');
        this.showFinalScoreAnimation(finalScore, multiplier);
    }
}

// 数据接口定义
interface IBallScoreRecord {
    ballId: string;
    multiplierBase: number;    // 倍数基数（用户选择的球数量）
    rubberHits: number;        // 橡胶体碰撞次数
    baseScore: number;         // 基础分数（碰撞累积）
    finalScore: number;        // 最终分数（应用区域倍数后）
    isCompleted: boolean;      // 是否已完成计分
}

interface IRubberBodyState {
    rubberId: string;
    hasElasticity: boolean;    // 是否还有弹性
    totalHits: number;         // 总碰撞次数（所有球）
}
```

### 4.4 游戏桌面配置系统

#### 4.4.1 桌面元素管理
基于现有 `PinballGameTableController` 扩展：

```typescript
export class CakePartyTableController extends PinballGameTableController {
    private scoringBumpers: IScoringBumperConfig[] = [];
    private bonusZones: IBonusZoneConfig[] = [];

    public init() {
        super.init(); // 初始化基础桌面元素（边界、挡板等）
        this.initScoringElements();
        this.initBonusSystem();
        this.initSpecialEffects();
    }

    // 初始化计分元素
    private initScoringElements(): void {
        // 创建计分撞击器
        this.scoringBumpers = this.loadScoringBumperConfig();
        this.scoringBumpers.forEach((config, index) => {
            this.createScoringBumper(config, index);
        });

        // 创建橡胶挡板（现有实现扩展）
        this.initEnhancedRubbers();
    }

    // 初始化奖励系统
    private initBonusSystem(): void {
        this.bonusZones = this.loadBonusZoneConfig();
        this.bonusZones.forEach((config, index) => {
            this.createBonusZone(config, index);
        });
    }

    // 增强版橡胶挡板（添加防卡球机制）
    private initEnhancedRubbers(): void {
        PinballTableMap.rubbers.forEach((point, index) => {
            this.physicsCtr.createCircle({
                radius: 36,
                center: { x: point.x, y: point.y },
                friction: 0.2,
                restitution: 1,
                userData: {
                    id: `rubber_${index}`,
                    callback: () => {
                        // 防卡球机制：边缘橡胶给予侧向力
                        this.applyAntiStuckForce(index);

                        // 播放撞击音效
                        SoundController.playEffect('rubber_hit');
                    }
                }
            });
        });
    }

    // 防卡球力度应用
    private applyAntiStuckForce(rubberIndex: number): void {
        const totalRubbers = PinballTableMap.rubbers.length;

        if (rubberIndex === 0) {
            // 最左侧橡胶：向右推
            PhysicsUtil.applyLinearImpulse(this.view.ball, {
                x: randomRange(0.15, 0.25), y: 0
            });
        } else if (rubberIndex === totalRubbers - 1) {
            // 最右侧橡胶：向左推
            PhysicsUtil.applyLinearImpulse(this.view.ball, {
                x: -randomRange(0.15, 0.25), y: 0
            });
        }
    }
}
```

#### 4.4.2 动态难度调整
```typescript
export class DifficultyController {
    private currentDifficulty = 1;
    private playerPerformance: IPerformanceData;

    // 根据玩家表现调整难度
    public adjustDifficulty(gameResults: IGameResult[]): void {
        const avgScore = this.calculateAverageScore(gameResults);
        const targetScore = this.getTargetScore();

        if (avgScore > targetScore * 1.2) {
            this.increaseDifficulty();
        } else if (avgScore < targetScore * 0.8) {
            this.decreaseDifficulty();
        }
    }

    // 应用难度设置
    public applyDifficultySettings(): void {
        const settings = this.getDifficultySettings(this.currentDifficulty);

        // 调整撞击器弹性
        this.adjustBumperRestitution(settings.bumperRestitution);

        // 调整奖励区域大小
        this.adjustBonusZoneSize(settings.bonusZoneScale);

        // 调整弹珠物理参数
        this.adjustBallPhysics(settings.ballSettings);
    }
}
```

### 4.5 性能优化实现

#### 4.5.1 对象池管理
```typescript
export class PinballObjectPool {
    private ballPool: b2Body[] = [];
    private effectPool: egret.DisplayObject[] = [];
    private readonly MAX_BALLS = 50; // 最大弹珠数量

    // 获取弹珠对象
    public getBall(): b2Body {
        if (this.ballPool.length > 0) {
            const ball = this.ballPool.pop();
            this.resetBall(ball);
            return ball;
        }

        return this.createNewBall();
    }

    // 回收弹珠对象
    public recycleBall(ball: b2Body): void {
        if (this.ballPool.length < this.MAX_BALLS) {
            ball.SetEnabled(false);
            this.ballPool.push(ball);
        } else {
            // 池满时销毁对象
            this.physicsCtr.world.DestroyBody(ball);
        }
    }

    // 重置弹珠状态
    private resetBall(ball: b2Body): void {
        ball.SetEnabled(true);
        ball.SetLinearVelocity({ x: 0, y: 0 });
        ball.SetAngularVelocity(0);
        ball.GetUserData().ballId = this.generateBallId();
    }
}
```

#### 4.5.2 物理引擎优化
```typescript
export class PhysicsOptimizer {
    private activeBalls: Set<b2Body> = new Set();
    private readonly MAX_ACTIVE_BALLS = 10;

    // 限制活跃弹珠数量
    public manageBallCount(): void {
        if (this.activeBalls.size > this.MAX_ACTIVE_BALLS) {
            const oldestBall = this.getOldestBall();
            this.forceRemoveBall(oldestBall);
        }
    }

    // 动态调整物理精度
    public adjustPhysicsAccuracy(ballCount: number): void {
        let velocityIterations = 4;
        let positionIterations = 3;

        if (ballCount > 5) {
            velocityIterations = 3;
            positionIterations = 2;
        }

        this.physicsCtr.setIterations(velocityIterations, positionIterations);
    }

    // 休眠管理
    public manageSleeping(): void {
        this.activeBalls.forEach(ball => {
            const velocity = ball.GetLinearVelocity();
            const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);

            if (speed < 0.1) {
                ball.SetSleepingAllowed(true);
            }
        });
    }
}
```

## 5. 数据模型设计

### 5.1 核心数据结构

#### 5.1.1 游戏状态模型
```typescript
interface ICakePartyGameState {
    // 代币系统
    tokenBalance: number;
    tokenEarned: number;
    tokenConsumed: number;

    // 倍数系统
    currentMultiplier: number;
    availableMultipliers: number[];

    // 团队进度
    teamProgress: ICakeProgress[];
    currentCake: number;

    // 分数系统
    currentGameScore: number;
    totalScore: number;
    highScore: number;

    // 游戏状态
    gamePhase: EGamePhase;
    ballsInPlay: number;
    isGameActive: boolean;
}

interface ICakeProgress {
    cakeId: number;
    cakeType: ECakeType;
    currentStage: number;
    totalStages: number;
    requiredScore: number;
    currentScore: number;
    teamMember: ITeamMember;
    isCompleted: boolean;
    rewards: IReward[];
    milestones: IMilestone[];
}
```

#### 5.1.2 物理配置模型
```typescript
interface IPinballPhysicsConfig {
    // 世界参数
    gravity: number;
    simulateSpeed: number;
    worldScale: number;

    // 弹珠参数
    ballRadius: number;
    ballDensity: number;
    ballFriction: number;
    ballRestitution: number;

    // 弹射参数
    springMaxPower: number;
    springMinScale: number;
    launchAngleVariance: number;

    // 撞击器参数
    bumperRadius: number;
    bumperRestitution: number;
    maxBumperHits: number;

    // 性能参数
    maxActiveBalls: number;
    velocityIterations: number;
    positionIterations: number;
}
```

#### 5.1.3 弹珠数据模型
```typescript
interface IBallScore {
    ballId: string;
    launchTime: number;
    multiplier: number;

    // 撞击记录
    bumperHits: number;
    bumperScore: number;
    maxHitsReached: boolean;

    // 最终得分
    bonusMultiplier: number;
    finalScore: number;

    // 轨迹数据（用于回放）
    trajectory: ITrajectoryPoint[];
}

interface ITrajectoryPoint {
    timestamp: number;
    position: XY;
    velocity: XY;
    event?: string; // 'bumper_hit', 'bonus_zone', etc.
}
```

#### 5.1.4 游戏配置模型
```typescript
interface IScoringBumperConfig {
    id: string;
    position: XY;
    radius: number;
    restitution: number;
    scoreValue: number;
    effectType: string;
    soundEffect: string;
}

interface IBonusZoneConfig {
    id: string;
    position: XY;
    width: number;
    height: number;
    multiplier: number;
    probability: number; // 弹珠落入概率权重
    visualEffect: string;
    rewardType: ERewardType;
}
```

## 6. 视图层设计

### 6.1 弹珠游戏视图架构

#### 6.1.1 主视图控制器
```typescript
export class CakePartyPinballView extends PinballView {
    // 继承现有弹珠视图，扩展Cake Party特性
    public multiplierPanel: MultiplierPanel;
    public scorePanel: CakePartyScorePanel;
    public teamProgressPanel: TeamProgressPanel;
    public tokenDisplay: TokenDisplay;
    public cakeProgressDisplay: CakeProgressDisplay;

    // 游戏控制器
    private launchController: CakePartyLaunchController;
    private scoreController: CakePartyScoreController;
    private effectController: CakePartyEffectController;

    protected createChildren() {
        super.createChildren(); // 继承基础弹珠桌面
        this.initCakePartyUI();
        this.initControllers();
        this.setupEventListeners();
    }

    private initCakePartyUI(): void {
        // 初始化Cake Party特有UI组件
        this.multiplierPanel = new MultiplierPanel();
        this.scorePanel = new CakePartyScorePanel();
        this.teamProgressPanel = new TeamProgressPanel();
        this.tokenDisplay = new TokenDisplay();
        this.cakeProgressDisplay = new CakeProgressDisplay();

        this.addChild(this.multiplierPanel);
        this.addChild(this.scorePanel);
        this.addChild(this.teamProgressPanel);
        this.addChild(this.tokenDisplay);
        this.addChild(this.cakeProgressDisplay);
    }

    private initControllers(): void {
        this.launchController = new CakePartyLaunchController(this);
        this.scoreController = new CakePartyScoreController(this);
        this.effectController = new CakePartyEffectController(this);

        this.launchController.init();
        this.scoreController.init();
        this.effectController.init();
    }
}
```

#### 6.1.2 倍数选择面板
```typescript
export class MultiplierPanel extends eui.Component {
    public multiplierButton: eui.Button;
    public multiplierLabel: eui.Label;
    public tokenBalanceLabel: eui.Label;
    public insufficientTokensWarning: eui.Group;

    private currentMultiplier = 1;
    private availableMultipliers = [1, 5, 10, 20, 50];
    private tokenBalance = 0;

    protected createChildren() {
        super.createChildren();
        this.skinName = 'MultiplierPanelSkin';
        this.setupButtonEvents();
    }

    private setupButtonEvents(): void {
        this.multiplierButton.addEventListener(egret.TouchEvent.TOUCH_TAP,
            this.onMultiplierButtonTap, this);
    }

    private onMultiplierButtonTap(): void {
        const nextMultiplier = this.getNextMultiplier();
        this.setMultiplier(nextMultiplier);

        // 播放切换音效
        SoundController.playEffect('multiplier_switch');
    }

    public setMultiplier(multiplier: number): void {
        this.currentMultiplier = multiplier;
        this.multiplierLabel.text = `x${multiplier}`;

        // 检查代币是否足够
        const canAfford = this.tokenBalance >= multiplier;
        this.updateButtonState(canAfford);

        // 分发倍数变更事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.MULTIPLIER_CHANGED, {
            multiplier,
            canAfford
        });
    }

    public updateTokenBalance(balance: number): void {
        this.tokenBalance = balance;
        this.tokenBalanceLabel.text = balance.toString();

        // 自动选择合适的倍数（PRD需求）
        const autoMultiplier = this.calculateAutoMultiplier(balance);
        if (autoMultiplier !== this.currentMultiplier) {
            this.setMultiplier(autoMultiplier);
        }
    }

    private calculateAutoMultiplier(balance: number): number {
        if (balance >= 50) return 50;
        if (balance >= 20) return 20;
        if (balance >= 10) return 10;
        if (balance >= 5) return 5;
        return 1;
    }

    private updateButtonState(canAfford: boolean): void {
        this.multiplierButton.enabled = canAfford;
        this.insufficientTokensWarning.visible = !canAfford;

        if (canAfford) {
            this.multiplierButton.alpha = 1.0;
        } else {
            this.multiplierButton.alpha = 0.5;
        }
    }
}
```

#### 6.1.3 分数显示系统
```typescript
export class CakePartyScorePanel extends eui.Component {
    public currentScoreLabel: eui.Label;
    public totalScoreLabel: eui.Label;
    public scoreIncrementLabel: eui.Label;
    public comboLabel: eui.Label;

    private currentScore = 0;
    private totalScore = 0;
    private comboCount = 0;

    protected createChildren() {
        super.createChildren();
        this.skinName = 'CakePartyScorePanelSkin';
        this.initAnimations();
    }

    // 更新当前游戏分数
    public updateCurrentScore(newScore: number, increment: number): void {
        this.currentScore = newScore;

        // 播放分数增加动画
        this.playScoreIncrementAnimation(increment);

        // 更新显示
        this.currentScoreLabel.text = newScore.toString();
    }

    // 更新总分数
    public updateTotalScore(newTotal: number): void {
        const oldTotal = this.totalScore;
        this.totalScore = newTotal;

        // 播放总分数增加动画
        this.playTotalScoreAnimation(oldTotal, newTotal);
    }

    // 显示连击
    public showCombo(combo: number): void {
        if (combo <= 1) {
            this.comboLabel.visible = false;
            return;
        }

        this.comboCount = combo;
        this.comboLabel.text = `COMBO x${combo}`;
        this.comboLabel.visible = true;

        // 播放连击动画
        this.playComboAnimation();
    }

    private playScoreIncrementAnimation(increment: number): void {
        this.scoreIncrementLabel.text = `+${increment}`;
        this.scoreIncrementLabel.visible = true;
        this.scoreIncrementLabel.alpha = 1;
        this.scoreIncrementLabel.scaleX = this.scoreIncrementLabel.scaleY = 1;

        egret.Tween.get(this.scoreIncrementLabel)
            .to({
                alpha: 0,
                scaleX: 1.5,
                scaleY: 1.5,
                y: this.scoreIncrementLabel.y - 50
            }, 800)
            .call(() => {
                this.scoreIncrementLabel.visible = false;
                this.scoreIncrementLabel.y += 50; // 重置位置
            });
    }

    private playTotalScoreAnimation(oldScore: number, newScore: number): void {
        const duration = 1000;
        const startTime = egret.getTimer();

        const updateScore = () => {
            const elapsed = egret.getTimer() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentDisplayScore = Math.floor(oldScore + (newScore - oldScore) * progress);
            this.totalScoreLabel.text = currentDisplayScore.toString();

            if (progress < 1) {
                egret.setTimeout(updateScore, this, 16); // ~60fps
            }
        };

        updateScore();
    }
}
```

### 6.2 特效系统设计

#### 6.2.1 撞击特效控制器
```typescript
export class CakePartyEffectController {
    private effectPool: Map<string, egret.DisplayObject[]> = new Map();
    private activeEffects: Set<egret.DisplayObject> = new Set();

    public init(): void {
        this.preloadEffects();
        this.setupEventListeners();
    }

    private setupEventListeners(): void {
        SmartEvent.addEventListener(ECakePartyEventType.BUMPER_HIT,
            this.onBumperHit, this);
        SmartEvent.addEventListener(ECakePartyEventType.BALL_SCORED,
            this.onBallScored, this);
        SmartEvent.addEventListener(ECakePartyEventType.MILESTONE_REACHED,
            this.onMilestoneReached, this);
    }

    // 撞击器特效
    private onBumperHit(event: egret.Event): void {
        const { bumperIndex, hitCount } = event.data;
        const bumperPosition = this.getBumperPosition(bumperIndex);

        // 播放撞击特效
        this.playEffect('bumper_hit', bumperPosition);

        // 显示分数飞字
        this.showScorePopup(hitCount, bumperPosition);

        // 播放音效
        SoundController.playEffect('bumper_hit', { volume: 0.7 });
    }

    // 得分特效
    private onBallScored(event: egret.Event): void {
        const { zoneIndex, finalScore } = event.data;
        const zonePosition = this.getBonusZonePosition(zoneIndex);

        // 播放奖励区域特效
        this.playEffect('bonus_zone_hit', zonePosition);

        // 显示最终得分
        this.showFinalScorePopup(finalScore, zonePosition);

        // 播放得分音效
        SoundController.playEffect('score_achieved', { volume: 0.8 });
    }

    // 里程碑特效
    private onMilestoneReached(event: egret.Event): void {
        const { cakeIndex, milestone } = event.data;

        // 播放里程碑达成特效
        this.playFullScreenEffect('milestone_achieved');

        // 显示奖励弹窗
        this.showMilestoneRewardPopup(milestone);

        // 播放庆祝音效
        SoundController.playEffect('milestone_fanfare', { volume: 1.0 });
    }

    private playEffect(effectType: string, position: XY): void {
        const effect = this.getEffectFromPool(effectType);
        if (!effect) return;

        effect.x = position.x;
        effect.y = position.y;
        this.view.addChild(effect);
        this.activeEffects.add(effect);

        // 播放动画
        if (effect instanceof egret.MovieClip) {
            effect.gotoAndPlay(1, 1);
            effect.addEventListener(egret.Event.COMPLETE, () => {
                this.recycleEffect(effectType, effect);
            }, this);
        }
    }
}
```

## 7. 事件系统设计

### 7.1 游戏事件定义

```typescript
enum ECakePartyEventType {
    // 弹珠生命周期
    BALL_CREATED = 'ball_created',
    BALL_LAUNCHED = 'ball_launched',
    BALL_HIT_BUMPER = 'ball_hit_bumper',
    BALL_ENTER_BONUS_ZONE = 'ball_enter_bonus_zone',
    BALL_REMOVED = 'ball_removed',
    BALL_SCORED = 'ball_scored',

    // 撞击器系统
    BUMPER_HIT = 'bumper_hit',
    BUMPERS_DEACTIVATED = 'bumpers_deactivated',
    MAX_HITS_REACHED = 'max_hits_reached',

    // 分数系统
    SCORE_UPDATED = 'score_updated',
    SCORE_INCREMENT = 'score_increment',
    COMBO_ACHIEVED = 'combo_achieved',
    HIGH_SCORE_BEATEN = 'high_score_beaten',

    // 里程碑系统
    MILESTONE_REACHED = 'milestone_reached',
    CAKE_STAGE_COMPLETED = 'cake_stage_completed',
    CAKE_COMPLETED = 'cake_completed',
    ALL_CAKES_COMPLETED = 'all_cakes_completed',

    // 倍数系统
    MULTIPLIER_CHANGED = 'multiplier_changed',
    MULTIPLIER_AUTO_SELECTED = 'multiplier_auto_selected',

    // 代币系统
    TOKEN_CONSUMED = 'token_consumed',
    TOKEN_INSUFFICIENT = 'token_insufficient',
    TOKEN_BALANCE_UPDATED = 'token_balance_updated',

    // 团队系统
    TEAM_PROGRESS_UPDATED = 'team_progress_updated',
    FRIEND_SCORE_ADDED = 'friend_score_added',
    TEAM_MEMBER_ONLINE = 'team_member_online',

    // 游戏状态
    GAME_STARTED = 'game_started',
    GAME_PAUSED = 'game_paused',
    GAME_RESUMED = 'game_resumed',
    GAME_ENDED = 'game_ended',

    // UI事件
    UI_ANIMATION_COMPLETE = 'ui_animation_complete',
    POPUP_SHOWN = 'popup_shown',
    POPUP_CLOSED = 'popup_closed'
}
```

### 7.2 事件处理架构

#### 7.2.1 事件处理流程图

```mermaid
flowchart TD
    A[游戏事件触发] --> B[事件队列管理器]
    B --> C{事件优先级}

    C -->|高优先级| D[立即处理]
    C -->|普通优先级| E[加入处理队列]

    D --> F[事件处理器分发]
    E --> G[按帧限制处理]
    G --> F

    F --> H{事件类型}

    H -->|弹珠事件| I[弹珠事件处理器]
    H -->|分数事件| J[分数事件处理器]
    H -->|UI事件| K[UI事件处理器]
    H -->|音效事件| L[音效事件处理器]

    I --> M[更新物理状态]
    J --> N[更新分数显示]
    K --> O[更新UI组件]
    L --> P[播放音效]

    M --> Q[触发后续事件]
    N --> Q
    O --> Q
    P --> Q

    Q --> R[事件处理完成]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style F fill:#e8f5e8
    style H fill:#f3e5f5
```

#### 7.2.2 主事件处理器
```typescript
export class CakePartyEventHandler {
    private eventMap: Map<string, Function> = new Map();
    private eventQueue: IEventData[] = [];
    private isProcessing = false;

    public init(): void {
        this.registerEventHandlers();
        this.startEventProcessing();
    }

    private registerEventHandlers(): void {
        // 弹珠事件
        this.eventMap.set(ECakePartyEventType.BALL_LAUNCHED, this.onBallLaunched.bind(this));
        this.eventMap.set(ECakePartyEventType.BALL_HIT_BUMPER, this.onBallHitBumper.bind(this));
        this.eventMap.set(ECakePartyEventType.BALL_SCORED, this.onBallScored.bind(this));

        // 分数事件
        this.eventMap.set(ECakePartyEventType.SCORE_UPDATED, this.onScoreUpdated.bind(this));
        this.eventMap.set(ECakePartyEventType.MILESTONE_REACHED, this.onMilestoneReached.bind(this));

        // 倍数事件
        this.eventMap.set(ECakePartyEventType.MULTIPLIER_CHANGED, this.onMultiplierChanged.bind(this));

        // 代币事件
        this.eventMap.set(ECakePartyEventType.TOKEN_CONSUMED, this.onTokenConsumed.bind(this));

        // 注册所有事件监听
        this.eventMap.forEach((handler, eventType) => {
            SmartEvent.addEventListener(eventType, handler, this);
        });
    }

    // 弹珠发射事件处理
    private onBallLaunched(event: egret.Event): void {
        const { ballId, multiplier, launchPower } = event.data;

        // 记录发射数据
        this.recordBallLaunch(ballId, multiplier, launchPower);

        // 更新UI状态
        this.updateLaunchUI(multiplier);

        // 播放发射音效
        SoundController.playEffect('ball_launch');

        // 开始球的轨迹追踪
        this.startBallTracking(ballId);
    }

    // 撞击器碰撞事件处理
    private onBallHitBumper(event: egret.Event): void {
        const { ballId, bumperIndex, hitCount, scoreIncrement } = event.data;

        // 更新分数
        this.updateScore(scoreIncrement);

        // 检查连击
        this.checkCombo(ballId, hitCount);

        // 播放撞击特效
        this.playBumperEffect(bumperIndex, scoreIncrement);

        // 检查是否达到最大撞击次数
        if (hitCount >= 10) {
            SmartEvent.dispatchEventWith(ECakePartyEventType.MAX_HITS_REACHED, { ballId });
        }
    }

    // 得分事件处理
    private onBallScored(event: egret.Event): void {
        const { ballId, finalScore, zoneIndex, zoneMultiplier } = event.data;

        // 更新总分
        this.addToTotalScore(finalScore);

        // 检查里程碑
        this.checkMilestones(finalScore);

        // 播放得分特效
        this.playScoreEffect(finalScore, zoneIndex);

        // 记录得分数据
        this.recordBallScore(ballId, finalScore, zoneMultiplier);
    }

    // 里程碑达成事件处理
    private onMilestoneReached(event: egret.Event): void {
        const { cakeIndex, milestone, rewards } = event.data;

        // 播放里程碑特效
        this.playMilestoneEffect(milestone);

        // 显示奖励
        this.showMilestoneRewards(rewards);

        // 更新蛋糕进度
        this.updateCakeProgress(cakeIndex, milestone);

        // 检查是否完成蛋糕
        if (milestone.isComplete) {
            SmartEvent.dispatchEventWith(ECakePartyEventType.CAKE_COMPLETED, { cakeIndex });
        }
    }
}
```

#### 7.2.2 事件队列管理
```typescript
export class EventQueueManager {
    private eventQueue: IQueuedEvent[] = [];
    private isProcessing = false;
    private readonly MAX_EVENTS_PER_FRAME = 5;

    // 添加事件到队列
    public queueEvent(eventType: string, data: any, priority: number = 0): void {
        const event: IQueuedEvent = {
            type: eventType,
            data,
            priority,
            timestamp: egret.getTimer()
        };

        // 按优先级插入
        this.insertByPriority(event);

        // 开始处理队列
        if (!this.isProcessing) {
            this.processQueue();
        }
    }

    // 处理事件队列
    private processQueue(): void {
        if (this.eventQueue.length === 0) {
            this.isProcessing = false;
            return;
        }

        this.isProcessing = true;
        let processedCount = 0;

        while (this.eventQueue.length > 0 && processedCount < this.MAX_EVENTS_PER_FRAME) {
            const event = this.eventQueue.shift();
            this.dispatchEvent(event);
            processedCount++;
        }

        // 下一帧继续处理
        if (this.eventQueue.length > 0) {
            egret.setTimeout(this.processQueue, this, 16);
        } else {
            this.isProcessing = false;
        }
    }

    private dispatchEvent(event: IQueuedEvent): void {
        try {
            SmartEvent.dispatchEventWith(event.type, event.data);
        } catch (error) {
            console.error(`Error dispatching event ${event.type}:`, error);
        }
    }
}
```

## 8. 性能优化策略

### 8.1 Box2D物理引擎优化

#### 8.1.1 物理引擎优化流程图

```mermaid
flowchart TD
    A[游戏帧开始] --> B[计算帧时间差]
    B --> C[累积物理时间]
    C --> D{时间 >= 1/60s}

    D -->|否| E[跳过物理更新]
    D -->|是| F[检查活跃弹珠数量]

    F --> G{弹珠数量}
    G -->|> 8个| H[降低迭代精度]
    G -->|4-8个| I[中等迭代精度]
    G -->|< 4个| J[高迭代精度]

    H --> K[速度迭代3次<br/>位置迭代2次]
    I --> L[速度迭代4次<br/>位置迭代3次]
    J --> M[速度迭代6次<br/>位置迭代4次]

    K --> N[执行物理步进]
    L --> N
    M --> N

    N --> O[处理碰撞回调]
    O --> P[更新刚体状态]
    P --> Q[检查休眠对象]

    Q --> R{还有剩余时间}
    R -->|是| S[继续下一步]
    R -->|否| T[结束物理更新]

    S --> D
    E --> T
    T --> U[渲染更新]

    style A fill:#e3f2fd
    style F fill:#fff3e0
    style G fill:#f3e5f5
    style N fill:#e8f5e8
```

#### 8.1.2 物理世界优化实现
```typescript
export class PhysicsOptimizer {
    private readonly OPTIMAL_TIME_STEP = 1/60;
    private readonly MAX_SUB_STEPS = 3;
    private accumulatedTime = 0;

    // 固定时间步长更新
    public updatePhysics(deltaTime: number): void {
        this.accumulatedTime += deltaTime;

        let subSteps = 0;
        while (this.accumulatedTime >= this.OPTIMAL_TIME_STEP && subSteps < this.MAX_SUB_STEPS) {
            this.physicsCtr.world.Step(this.OPTIMAL_TIME_STEP, {
                velocityIterations: this.getOptimalVelocityIterations(),
                positionIterations: this.getOptimalPositionIterations()
            });

            this.accumulatedTime -= this.OPTIMAL_TIME_STEP;
            subSteps++;
        }
    }

    // 动态调整迭代次数
    private getOptimalVelocityIterations(): number {
        const activeBallCount = this.getActiveBallCount();
        if (activeBallCount > 8) return 3;
        if (activeBallCount > 4) return 4;
        return 6;
    }

    private getOptimalPositionIterations(): number {
        const activeBallCount = this.getActiveBallCount();
        if (activeBallCount > 8) return 2;
        if (activeBallCount > 4) return 3;
        return 4;
    }
}
```

#### 8.1.2 碰撞检测优化
```typescript
export class CollisionOptimizer {
    // 碰撞分组优化
    public setupCollisionFilters(): void {
        const BALL_CATEGORY = 0x0001;
        const BUMPER_CATEGORY = 0x0002;
        const WALL_CATEGORY = 0x0004;
        const SENSOR_CATEGORY = 0x0008;

        // 弹珠只与撞击器、墙壁、传感器碰撞
        this.setBallCollisionFilter({
            categoryBits: BALL_CATEGORY,
            maskBits: BUMPER_CATEGORY | WALL_CATEGORY | SENSOR_CATEGORY
        });

        // 撞击器只与弹珠碰撞
        this.setBumperCollisionFilter({
            categoryBits: BUMPER_CATEGORY,
            maskBits: BALL_CATEGORY
        });
    }

    // 空间分区优化
    public enableSpatialPartitioning(): void {
        // Box2D内置的broad-phase已经很高效，主要优化在于合理设置AABB
        this.physicsCtr.world.SetBroadPhaseCallback(this.customBroadPhaseCallback.bind(this));
    }
}
```

### 8.2 渲染性能优化

#### 8.2.1 对象池管理
```typescript
export class RenderObjectPool {
    private pools: Map<string, any[]> = new Map();
    private readonly POOL_SIZES = {
        'score_popup': 20,
        'effect_particle': 50,
        'ball_trail': 10,
        'ui_tween': 30
    };

    // 获取对象
    public getObject<T>(type: string, createFn: () => T): T {
        const pool = this.getPool(type);

        if (pool.length > 0) {
            return pool.pop() as T;
        }

        return createFn();
    }

    // 回收对象
    public recycleObject(type: string, obj: any): void {
        const pool = this.getPool(type);
        const maxSize = this.POOL_SIZES[type] || 10;

        if (pool.length < maxSize) {
            this.resetObject(obj);
            pool.push(obj);
        }
    }

    private resetObject(obj: any): void {
        if (obj instanceof egret.DisplayObject) {
            obj.visible = false;
            obj.alpha = 1;
            obj.scaleX = obj.scaleY = 1;
            obj.rotation = 0;
        }
    }
}
```

#### 8.2.2 批量渲染优化
```typescript
export class BatchRenderer {
    private renderQueue: IRenderCommand[] = [];
    private readonly MAX_BATCH_SIZE = 100;

    // 批量更新分数显示
    public batchUpdateScores(updates: IScoreUpdate[]): void {
        const batches = this.createBatches(updates, this.MAX_BATCH_SIZE);

        batches.forEach((batch, index) => {
            egret.setTimeout(() => {
                this.processBatch(batch);
            }, this, index * 16); // 分帧处理
        });
    }

    // 合并相似的渲染操作
    public optimizeRenderCommands(): void {
        const grouped = this.groupSimilarCommands(this.renderQueue);

        grouped.forEach(group => {
            this.executeBatchCommand(group);
        });

        this.renderQueue.length = 0;
    }
}
```

### 8.3 内存管理优化

#### 8.3.1 资源生命周期管理
```typescript
export class ResourceManager {
    private loadedTextures: Map<string, egret.Texture> = new Map();
    private textureUsageCount: Map<string, number> = new Map();
    private readonly MAX_TEXTURE_CACHE = 50;

    // 智能纹理缓存
    public getTexture(key: string): egret.Texture {
        let texture = this.loadedTextures.get(key);

        if (!texture) {
            texture = RES.getRes(key);
            this.cacheTexture(key, texture);
        }

        this.incrementUsage(key);
        return texture;
    }

    // 释放未使用的纹理
    public cleanupUnusedTextures(): void {
        const unusedTextures: string[] = [];

        this.textureUsageCount.forEach((count, key) => {
            if (count === 0) {
                unusedTextures.push(key);
            }
        });

        unusedTextures.forEach(key => {
            this.releaseTexture(key);
        });
    }

    // 内存压力监控
    public monitorMemoryPressure(): void {
        const memoryInfo = (performance as any).memory;
        if (memoryInfo && memoryInfo.usedJSHeapSize > memoryInfo.totalJSHeapSize * 0.8) {
            this.performEmergencyCleanup();
        }
    }
}
```

#### 8.3.2 音效管理优化
```typescript
export class AudioManager {
    private audioPool: Map<string, egret.Sound[]> = new Map();
    private playingAudios: Set<egret.SoundChannel> = new Set();
    private readonly MAX_CONCURRENT_SOUNDS = 8;

    // 限制并发音效数量
    public playEffect(key: string, options?: any): egret.SoundChannel {
        if (this.playingAudios.size >= this.MAX_CONCURRENT_SOUNDS) {
            this.stopOldestSound();
        }

        const sound = this.getSound(key);
        const channel = sound.play(0, 1);

        if (channel) {
            this.playingAudios.add(channel);
            channel.addEventListener(egret.Event.SOUND_COMPLETE, () => {
                this.playingAudios.delete(channel);
            }, this);
        }

        return channel;
    }

    // 音效预加载
    public preloadGameAudio(): Promise<void> {
        const audioKeys = [
            'ball_launch', 'bumper_hit', 'score_achieved',
            'milestone_fanfare', 'rubber_hit', 'multiplier_switch'
        ];

        return Promise.all(audioKeys.map(key => this.loadAudio(key)))
            .then(() => console.log('Game audio preloaded'));
    }
}
```

## 9. 开发计划与风险评估

### 9.1 详细开发阶段

#### 9.1.1 开发时间线甘特图

```mermaid
gantt
    title Cake Party 弹珠游戏开发计划
    dateFormat  YYYY-MM-DD
    section Phase 1: 基础架构
    场景架构搭建           :p1-1, 2024-01-01, 1d
    MVC框架集成           :p1-2, after p1-1, 1d
    事件系统创建           :p1-3, after p1-2, 1d

    section Phase 2: 物理系统
    弹射机制实现           :p2-1, after p1-3, 2d
    撞击器系统             :p2-2, after p2-1, 2d

    section Phase 3: UI开发
    倍数面板              :p3-1, after p2-2, 1d
    分数面板              :p3-2, after p3-1, 1d
    特效系统              :p3-3, after p3-2, 1d

    section Phase 4: 分数集成
    分数计算逻辑           :p4-1, after p3-3, 1d
    团队进度同步           :p4-2, after p4-1, 1d

    section Phase 5: 测试优化
    性能测试              :p5-1, after p4-2, 1d
    集成测试              :p5-2, after p5-1, 1d
    最终调优              :p5-3, after p5-2, 1d
```

#### 9.1.2 开发阶段详情

#### Phase 1: 基础架构搭建 (3天)
- **Day 1**:
  - 创建CakePartyPinballScene基础架构
  - 集成现有PhysicsController
  - 搭建MVC框架结构
- **Day 2**:
  - 实现MultiplierController
  - 创建基础事件系统
  - 设置开发调试工具
- **Day 3**:
  - 集成现有PinballView
  - 实现基础UI框架
  - 完成架构单元测试

#### Phase 2: 弹珠物理系统 (4天)
- **Day 1-2**:
  - 扩展PinballGameStartController
  - 实现多倍数弹射机制
  - 优化弹珠创建和管理
- **Day 3-4**:
  - 实现ScoringBumperController
  - 创建BonusZoneController
  - 完成碰撞检测和分数计算

#### Phase 3: UI界面开发 (3天)
- **Day 1**:
  - 实现MultiplierPanel
  - 创建TokenDisplay组件
- **Day 2**:
  - 开发CakePartyScorePanel
  - 实现TeamProgressPanel
- **Day 3**:
  - 集成特效系统
  - 完成UI动画和交互

#### Phase 4: 分数系统集成 (2天)
- **Day 1**:
  - 实现分数计算逻辑
  - 集成里程碑系统
- **Day 2**:
  - 完成团队进度同步
  - 实现数据持久化

#### Phase 5: 测试优化 (3天)
- **Day 1**:
  - 性能测试和优化
  - 内存泄漏检查
- **Day 2**:
  - 集成测试
  - 边界情况处理
- **Day 3**:
  - 最终调优
  - 文档完善

### 9.2 技术风险与应对策略

#### 9.2.1 风险评估矩阵图

```mermaid
quadrantChart
    title 技术风险评估矩阵
    x-axis 低影响 --> 高影响
    y-axis 低概率 --> 高概率

    quadrant-1 监控区域
    quadrant-2 重点关注
    quadrant-3 接受风险
    quadrant-4 立即处理

    多倍数弹珠同步: [0.8, 0.7]
    Box2D性能调优: [0.9, 0.6]
    内存泄漏: [0.7, 0.5]
    网络同步延迟: [0.6, 0.4]
    跨平台兼容: [0.5, 0.3]
    UI响应延迟: [0.4, 0.2]
    音效播放冲突: [0.3, 0.3]
```

#### 9.2.2 高风险项应对策略
1. **多倍数弹珠物理同步**
   - 风险：大量弹珠同时存在导致性能下降
   - 应对：实现弹珠对象池，限制最大并发数量，分批发射
   - 监控指标：FPS、内存使用率、活跃弹珠数量

2. **Box2D性能调优**
   - 风险：物理计算消耗过多CPU资源
   - 应对：动态调整迭代次数，优化碰撞分组，使用固定时间步长
   - 监控指标：物理更新耗时、CPU使用率

3. **内存管理**
   - 风险：频繁创建销毁对象导致内存泄漏
   - 应对：实现完善的对象池系统，定期内存清理
   - 监控指标：内存使用量、GC频率

#### 9.2.3 中风险项应对策略
1. **网络同步延迟**
   - 风险：团队进度同步不及时
   - 应对：实现本地缓存机制，异步更新策略
   - 监控指标：网络延迟、同步成功率

2. **跨平台兼容性**
   - 风险：不同设备性能差异
   - 应对：实现动态画质调整，性能分级
   - 监控指标：不同设备的FPS表现

### 9.3 质量保证策略

#### 9.3.1 自动化测试
```typescript
// 单元测试示例
describe('CakePartyScoreCalculator', () => {
    it('should calculate correct score with multiplier', () => {
        const calculator = new CakePartyScoreCalculator();
        const result = calculator.calculateFinalScore('ball_1', 2);
        expect(result).toBe(expectedScore);
    });

    it('should limit bumper hits to maximum', () => {
        const calculator = new CakePartyScoreCalculator();
        // 测试最大撞击次数限制
    });
});
```

#### 9.3.2 性能基准测试
```typescript
export class PerformanceBenchmark {
    public runBenchmarks(): void {
        this.benchmarkPhysicsPerformance();
        this.benchmarkRenderingPerformance();
        this.benchmarkMemoryUsage();
    }

    private benchmarkPhysicsPerformance(): void {
        const startTime = performance.now();
        // 模拟50个弹珠同时运行
        for (let i = 0; i < 50; i++) {
            this.simulateBallPhysics();
        }
        const endTime = performance.now();
        console.log(`Physics benchmark: ${endTime - startTime}ms`);
    }
}
```

## 10. 部署与配置管理

### 10.1 资源管理策略

#### 10.1.1 资源分包策略
```json
{
  "groups": [
    {
      "name": "pinball_core",
      "keys": "pinball_table,ball_texture,spring_animation"
    },
    {
      "name": "pinball_effects",
      "keys": "bumper_hit_effect,score_popup,milestone_effect"
    },
    {
      "name": "pinball_audio",
      "keys": "ball_launch_sound,bumper_hit_sound,score_sound"
    }
  ]
}
```

#### 10.1.2 动态加载管理
```typescript
export class ResourceLoader {
    public async loadPinballResources(): Promise<void> {
        // 核心资源优先加载
        await RES.loadGroup('pinball_core');

        // 特效资源后台加载
        RES.loadGroup('pinball_effects');

        // 音效资源按需加载
        this.preloadAudioOnUserInteraction();
    }

    private preloadAudioOnUserInteraction(): void {
        const loadAudio = () => {
            RES.loadGroup('pinball_audio');
            document.removeEventListener('touchstart', loadAudio);
        };
        document.addEventListener('touchstart', loadAudio, { once: true });
    }
}
```

### 10.2 配置管理系统

#### 10.2.1 游戏参数配置
```typescript
interface IPinballGameConfig {
    physics: {
        gravity: number;
        ballDensity: number;
        springPower: number;
    };
    gameplay: {
        maxBumperHits: number;
        multipliers: number[];
        bonusZoneMultipliers: number[];
    };
    performance: {
        maxActiveBalls: number;
        physicsIterations: {
            velocity: number;
            position: number;
        };
    };
    ui: {
        animationDuration: number;
        effectIntensity: number;
    };
}
```

#### 10.2.2 A/B测试支持
```typescript
export class ABTestManager {
    private testConfigs: Map<string, any> = new Map();

    public getConfig(key: string, defaultValue: any): any {
        const testGroup = this.getUserTestGroup();
        const testConfig = this.testConfigs.get(`${key}_${testGroup}`);

        return testConfig !== undefined ? testConfig : defaultValue;
    }

    // 支持的A/B测试项
    public initABTests(): void {
        this.testConfigs.set('spring_power_A', 12.0);
        this.testConfigs.set('spring_power_B', 15.0);

        this.testConfigs.set('bumper_restitution_A', 1.0);
        this.testConfigs.set('bumper_restitution_B', 1.2);
    }
}
```
