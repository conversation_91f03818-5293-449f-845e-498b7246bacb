# Cake Party 弹珠游戏前端技术方案

## 1. 项目概述

### 1.1 需求背景
基于PRD v6.15 - Cake Party中的9.6 Pinball游戏需求，实现一个社交化的弹珠小游戏。用户通过组队合作，使用通关主线关卡获得的代币来玩弹珠游戏，获取分数来制作蛋糕并获得奖励。

### 1.2 核心功能
- 弹珠物理引擎游戏
- 倍数选择机制（x1, x5, x10, x20, x50）
- 分数计算与累积
- 团队协作进度展示
- 蛋糕制作进度管理

## 2. 技术架构设计

### 2.1 整体架构
参考现有Monster游戏场景管理模式，采用MVC架构：

```
CakePartyPinballScene (场景管理)
├── CakePartyGameManager (游戏管理器)
├── CakePartyPinballController (弹珠控制器)
├── CakePartyPinballView (视图层)
└── CakePartyPinballModel (数据模型)
```

### 2.2 核心模块设计

#### 2.2.1 场景管理 (CakePartyPinballScene)
基于 `MonsterGameScene` 的场景管理模式：

```typescript
export class CakePartyPinballScene extends GameBaseScene {
    public gameMgr: CakePartyGameManager;
    
    protected async init(data: ICakePartySceneData) {
        // 场景初始化
        await this.initGameMgr(data);
        this.initController();
        this.addEventListeners();
    }
    
    private async initGameMgr(data: ICakePartySceneData) {
        this.gameMgr = new CakePartyGameManager();
        await this.gameMgr.init(data, this);
    }
}
```

#### 2.2.2 游戏管理器 (CakePartyGameManager)
参考 `MonsterGameManager` 架构：

```typescript
export class CakePartyGameManager extends BaseGameManager {
    private pinballController: CakePartyPinballController;
    private scoreManager: CakePartyScoreManager;
    private teamManager: CakePartyTeamManager;
    
    public async init(data: ICakePartyInitData, parent: IGameView) {
        super.init(data, parent);
        this.initPinballGame();
        this.initScoreSystem();
        this.initTeamSystem();
    }
}
```

#### 2.2.3 弹珠控制器 (CakePartyPinballController)
基于现有 `PinballGameStartController` 和 `PinballGameTableController`：

```typescript
export class CakePartyPinballController {
    private physicsController: PhysicsController;
    private gameStartController: PinballGameStartController;
    private gameTableController: PinballGameTableController;
    private multiplierController: MultiplierController;
    
    public init() {
        this.initPhysicsWorld();
        this.initGameTable();
        this.initMultiplierSystem();
    }
}
```

## 3. 核心功能实现

### 3.1 物理引擎系统
复用现有 `PhysicsController` 的Box2D物理引擎：

```typescript
// 弹珠创建
public createBall(): b2Body {
    return this.physicsController.createBall();
}

// 弹射机制
public launchBall(multiplier: number) {
    const power = this.calculateLaunchPower();
    const balls = this.createMultipleBalls(multiplier);
    balls.forEach(ball => this.applyLaunchForce(ball, power));
}
```

### 3.2 倍数选择系统

```typescript
export class MultiplierController {
    private multipliers = [1, 5, 10, 20, 50];
    private currentMultiplier = 1;
    
    public autoSelectMultiplier(tokenBalance: number) {
        if (tokenBalance >= 50) return 50;
        if (tokenBalance >= 20) return 20;
        if (tokenBalance >= 10) return 10;
        if (tokenBalance >= 5) return 5;
        return 1;
    }
    
    public validateLaunch(tokenBalance: number): boolean {
        return tokenBalance >= this.currentMultiplier;
    }
}
```

### 3.3 分数计算系统

```typescript
export class ScoreCalculator {
    private readonly MAX_BUMPER_HITS = 10;
    
    public calculateScore(bumperHits: number, bonusZone: number, multiplier: number): number {
        const bumperScore = Math.min(bumperHits, this.MAX_BUMPER_HITS) * multiplier;
        const finalScore = bumperScore * bonusZone;
        return finalScore;
    }
    
    public updateTeamProgress(score: number, cakeIndex: number) {
        // 更新团队蛋糕制作进度
        this.teamManager.addScore(cakeIndex, score);
    }
}
```

### 3.4 游戏桌面配置
基于现有 `PinballGameTableController` 扩展：

```typescript
export class CakePartyTableController extends PinballGameTableController {
    public init() {
        super.init();
        this.initScoringBumpers();
        this.initBonusZones();
        this.initSpecialElements();
    }
    
    private initScoringBumpers() {
        // 创建计分撞击器
        this.scoringBumpers.forEach((config, index) => {
            this.createScoringBumper(config, index);
        });
    }
    
    private initBonusZones() {
        // 创建底部奖励区域
        this.bonusZones.forEach((config, index) => {
            this.createBonusZone(config, index);
        });
    }
}
```

## 4. 数据模型设计

### 4.1 游戏状态模型

```typescript
interface ICakePartyGameState {
    tokenBalance: number;
    currentMultiplier: number;
    teamProgress: ICakeProgress[];
    currentCake: number;
    totalScore: number;
}

interface ICakeProgress {
    cakeId: number;
    currentStage: number;
    totalStages: number;
    requiredScore: number;
    currentScore: number;
    teamMember: ITeamMember;
    isCompleted: boolean;
}
```

### 4.2 物理配置模型

```typescript
interface IPinballPhysicsConfig {
    ballRadius: number;
    ballDensity: number;
    ballFriction: number;
    springMaxPower: number;
    gravity: number;
    simulateSpeed: number;
}
```

## 5. 视图层设计

### 5.1 主界面布局

```typescript
export class CakePartyPinballView extends eui.Component {
    // UI组件
    public pinballTable: egret.DisplayObjectContainer;
    public multiplierPanel: MultiplierPanel;
    public scorePanel: ScorePanel;
    public teamProgressPanel: TeamProgressPanel;
    public tokenDisplay: TokenDisplay;
    
    protected createChildren() {
        super.createChildren();
        this.initPinballTable();
        this.initUIComponents();
    }
}
```

### 5.2 UI组件设计

```typescript
// 倍数选择面板
export class MultiplierPanel extends eui.Component {
    public multiplierButtons: eui.Button[];
    public currentMultiplierLabel: eui.Label;
    
    public updateMultiplier(multiplier: number) {
        this.currentMultiplierLabel.text = `x${multiplier}`;
        this.updateButtonStates(multiplier);
    }
}

// 分数显示面板
export class ScorePanel extends eui.Component {
    public currentScoreLabel: eui.Label;
    public scoreAnimation: egret.tween.TweenGroup;
    
    public updateScore(newScore: number, animated: boolean = true) {
        if (animated) {
            this.playScoreAnimation(newScore);
        } else {
            this.currentScoreLabel.text = newScore.toString();
        }
    }
}
```

## 6. 事件系统设计

### 6.1 游戏事件定义

```typescript
enum ECakePartyEventType {
    // 弹珠相关
    BALL_LAUNCHED = 'ball_launched',
    BALL_HIT_BUMPER = 'ball_hit_bumper',
    BALL_ENTER_BONUS_ZONE = 'ball_enter_bonus_zone',
    
    // 分数相关
    SCORE_UPDATED = 'score_updated',
    MILESTONE_REACHED = 'milestone_reached',
    CAKE_COMPLETED = 'cake_completed',
    
    // 倍数相关
    MULTIPLIER_CHANGED = 'multiplier_changed',
    TOKEN_INSUFFICIENT = 'token_insufficient',
    
    // 团队相关
    TEAM_PROGRESS_UPDATED = 'team_progress_updated',
    FRIEND_SCORE_ADDED = 'friend_score_added'
}
```

### 6.2 事件处理机制

```typescript
export class CakePartyEventHandler {
    private eventMap = {
        [ECakePartyEventType.BALL_HIT_BUMPER]: this.onBallHitBumper.bind(this),
        [ECakePartyEventType.SCORE_UPDATED]: this.onScoreUpdated.bind(this),
        [ECakePartyEventType.MILESTONE_REACHED]: this.onMilestoneReached.bind(this),
    };
    
    public init() {
        Object.keys(this.eventMap).forEach(eventType => {
            SmartEvent.addEventListener(eventType, this.eventMap[eventType], this);
        });
    }
}
```

## 7. 性能优化策略

### 7.1 物理引擎优化
- 复用现有物理对象池
- 限制同时存在的弹珠数量
- 优化碰撞检测频率

### 7.2 渲染优化
- 使用对象池管理UI组件
- 批量更新分数显示
- 优化动画性能

### 7.3 内存管理
- 及时清理物理刚体
- 复用纹理资源
- 控制音效播放数量

## 8. 开发计划

### 8.1 开发阶段
1. **Phase 1**: 基础架构搭建 (3天)
2. **Phase 2**: 弹珠物理系统 (4天)
3. **Phase 3**: UI界面开发 (3天)
4. **Phase 4**: 分数系统集成 (2天)
5. **Phase 5**: 测试优化 (3天)

### 8.2 技术风险
- Box2D物理引擎性能调优
- 多倍数弹珠同步处理
- 网络同步延迟处理

## 9. 测试策略

### 9.1 单元测试
- 分数计算逻辑测试
- 倍数选择逻辑测试
- 物理碰撞检测测试

### 9.2 集成测试
- 完整游戏流程测试
- 团队协作功能测试
- 异常情况处理测试

## 10. 部署方案

### 10.1 资源管理
- 弹珠游戏资源独立打包
- 按需加载物理引擎资源
- 优化纹理资源大小

### 10.2 配置管理
- 游戏参数可配置化
- 支持热更新配置
- A/B测试支持
