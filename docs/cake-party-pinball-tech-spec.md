# Cake Party 弹珠游戏前端技术方案

## 1. 项目概述

### 1.1 需求背景
基于PRD v6.15 - Cake Party中的9.6 Pinball游戏需求，实现一个社交化的弹珠小游戏。用户通过组队合作，使用通关主线关卡获得的代币来玩弹珠游戏，获取分数来制作蛋糕并获得奖励。

### 1.2 核心功能
- 弹珠物理引擎游戏
- 倍数选择机制（x1, x5, x10, x20, x50）
- 分数计算与累积
- 团队协作进度展示
- 蛋糕制作进度管理

### 1.3 整体游戏流程图

```mermaid
flowchart TD
    A[进入Cake Party弹珠游戏] --> B{检查团队状态}
    B -->|未组队| C[显示组队界面]
    B -->|已组队| D[选择蛋糕]

    C --> E[邀请好友/加入团队]
    E --> F{组队成功}
    F -->|失败| C
    F -->|成功| D

    D --> G[进入弹珠游戏界面]
    G --> H[显示代币余额]
    H --> I[自动选择倍数]

    I --> J{代币是否充足}
    J -->|不足| K[提示去主线获取代币]
    J -->|充足| L[用户拉动弹簧]

    K --> M[跳转主线游戏]
    M --> N[获得代币后返回]
    N --> H

    L --> O[释放弹簧发射弹珠]
    O --> P[弹珠在桌面运动]
    P --> Q[撞击计分器]

    Q --> R{撞击次数 < 10}
    R -->|是| S[累积分数]
    R -->|否| T[停用撞击器]

    S --> U[弹珠继续运动]
    T --> U
    U --> V[弹珠进入奖励区域]

    V --> W[计算最终得分]
    W --> X[更新团队进度]
    X --> Y{达成里程碑}

    Y -->|是| Z[播放里程碑特效]
    Y -->|否| AA[继续游戏]

    Z --> BB{蛋糕完成}
    BB -->|否| AA
    BB -->|是| CC[蛋糕完成庆祝]

    CC --> DD{所有蛋糕完成}
    DD -->|否| EE[切换下个蛋糕]
    DD -->|是| FF[获得大奖]

    EE --> D
    AA --> GG{还有代币}
    GG -->|是| I
    GG -->|否| K

    FF --> HH[游戏结束]

    style A fill:#e3f2fd
    style D fill:#e8f5e8
    style O fill:#fff3e0
    style W fill:#f3e5f5
    style FF fill:#ffebee
```

## 2. 技术架构设计

### 2.1 整体架构
参考现有Monster游戏场景管理模式，采用MVC架构：

```
CakePartyPinballScene (场景管理)
├── CakePartyGameManager (游戏管理器)
├── CakePartyPinballController (弹珠控制器)
├── CakePartyPinballView (视图层)
└── CakePartyPinballModel (数据模型)
```

### 2.2 核心模块设计

#### 2.2.1 系统架构流程图

```mermaid
graph TB
    A[CakePartyPinballScene] --> B[CakePartyGameManager]
    B --> C[CakePartyPinballController]
    B --> D[CakePartyScoreManager]
    B --> E[CakePartyTeamManager]

    C --> F[PhysicsController]
    C --> G[LaunchController]
    C --> H[TableController]
    C --> I[MultiplierController]

    F --> J[Box2D World]
    G --> K[Spring System]
    H --> L[Bumpers & Zones]
    I --> M[Token Management]

    D --> N[Score Calculator]
    D --> O[Milestone Tracker]
    E --> P[Team Progress]
    E --> Q[Friend Sync]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

#### 2.2.2 场景管理 (CakePartyPinballScene)
基于 `MonsterGameScene` 的场景管理模式：

```typescript
export class CakePartyPinballScene extends GameBaseScene {
    public gameMgr: CakePartyGameManager;

    protected async init(data: ICakePartySceneData) {
        // 场景初始化
        await this.initGameMgr(data);
        this.initController();
        this.addEventListeners();
    }

    private async initGameMgr(data: ICakePartySceneData) {
        this.gameMgr = new CakePartyGameManager();
        await this.gameMgr.init(data, this);
    }
}
```

#### 2.2.3 游戏管理器 (CakePartyGameManager)
参考 `MonsterGameManager` 架构：

```typescript
export class CakePartyGameManager extends BaseGameManager {
    private pinballController: CakePartyPinballController;
    private scoreManager: CakePartyScoreManager;
    private teamManager: CakePartyTeamManager;

    public async init(data: ICakePartyInitData, parent: IGameView) {
        super.init(data, parent);
        this.initPinballGame();
        this.initScoreSystem();
        this.initTeamSystem();
    }
}
```

#### 2.2.4 弹珠控制器 (CakePartyPinballController)
基于现有 `PinballGameStartController` 和 `PinballGameTableController`：

```typescript
export class CakePartyPinballController {
    private physicsController: PhysicsController;
    private gameStartController: PinballGameStartController;
    private gameTableController: PinballGameTableController;
    private multiplierController: MultiplierController;

    public init() {
        this.initPhysicsWorld();
        this.initGameTable();
        this.initMultiplierSystem();
    }
}
```

## 3. Box2D物理引擎调研

### 3.1 现有Box2D架构分析
基于现有代码分析，项目已集成Box2D物理引擎，具备以下特性：

#### 3.1.1 物理世界管理
```typescript
// 现有PhysicsController核心功能
export class PhysicsController {
    public world: b2World;  // Box2D物理世界实例

    public buildWorld(): void {
        // 创建物理世界，设置重力
        this.world = b2World.Create({ x: 0, y: pinballPhysicsConfig.gravity });
        this.createContactListener(); // 碰撞监听
    }

    public updateWorld(): boolean {
        // 物理步进：时间步长1/60s，速度迭代4次，位置迭代3次
        this.world.Step(1 / pinballPhysicsConfig.simulateSpeed,
                        { velocityIterations: 4, positionIterations: 3 });
        return false;
    }
}
```

#### 3.1.2 刚体创建与管理
```typescript
// 弹珠刚体创建（现有实现）
public createBall(): b2Body {
    const params: IPhysicsCircleParams = {
        radius: pinballConfig.ballRadius,
        bodyType: b2BodyType.b2_dynamicBody,  // 动态刚体
        bullet: true,                         // 高速碰撞检测
        allowSleep: false,                    // 禁止休眠
        density: pinballPhysicsConfig.ballDensity,
        friction: pinballPhysicsConfig.ballFriction,
        restitution: 0.2,                     // 弹性系数
        filter: {
            categoryBits: 0x3,                // 碰撞分类
            maskBits: 0xffff,                 // 碰撞掩码
        },
        userData: { id: 'ball' }
    };
    return this.createCircle(params);
}
```

#### 3.1.3 碰撞检测机制
```typescript
// 现有碰撞监听器实现
private createContactListener(): void {
    const listener = new b2ContactListener();

    listener.BeginContact = (contact: b2Contact) => {
        const bodyA = contact.GetFixtureA().GetBody();
        const bodyB = contact.GetFixtureB().GetBody();

        // 处理碰撞回调
        this.handleCollision(bodyA, bodyB);
    };

    this.world.SetContactListener(listener);
}
```

### 3.2 Box2D性能特性分析

#### 3.2.1 优势
- **高精度物理模拟**：支持连续碰撞检测（CCD），适合高速弹珠
- **稳定的数值计算**：使用定点数运算，避免浮点误差累积
- **丰富的刚体类型**：静态、动态、运动学刚体满足不同需求
- **灵活的约束系统**：支持关节、马达等复杂机械结构

#### 3.2.2 性能考量
- **计算开销**：物理步进需要固定时间片，建议60FPS
- **内存占用**：每个刚体约占用200-400字节
- **碰撞检测**：O(n²)复杂度，需要合理设计碰撞分组

### 3.3 弹珠游戏物理参数调优

#### 3.3.1 关键参数配置
```typescript
interface IPinballPhysicsConfig {
    // 物理世界参数
    gravity: 9.8,                    // 重力加速度
    simulateSpeed: 60,               // 模拟频率60Hz

    // 弹珠参数
    ballRadius: 12,                  // 弹珠半径(像素)
    ballDensity: 1.0,               // 弹珠密度
    ballFriction: 0.1,              // 弹珠摩擦系数
    ballRestitution: 0.6,           // 弹珠弹性系数

    // 弹射参数
    springMaxPower: 15.0,           // 弹簧最大力度
    launchAngleVariance: 0.1,       // 发射角度随机性

    // 撞击器参数
    bumperRestitution: 1.2,         // 撞击器弹性(>1产生加速)
    bumperRadius: 36,               // 撞击器半径
}
```

#### 3.3.2 物理材质设计
```typescript
// 不同游戏元素的物理属性
const PHYSICS_MATERIALS = {
    BALL: { density: 1.0, friction: 0.1, restitution: 0.6 },
    BUMPER: { density: 0, friction: 0.2, restitution: 1.2 },      // 加速撞击器
    WALL: { density: 0, friction: 0, restitution: 0 },       // 边界墙壁
    RUBBER: { density: 0, friction: 0.2, restitution: 1.0 },     // 橡胶挡板
    SENSOR: { density: 0, friction: 0, restitution: 0, isSensor: true }, // 传感器区域
};
```

## 4. 弹珠游戏核心实现

### 4.1 弹射系统实现

#### 4.1.1 弹珠发射流程图

```mermaid
flowchart TD
    A[用户触摸弹簧区域] --> B{检查代币余额}
    B -->|余额不足| C[显示代币不足提示]
    B -->|余额充足| D[开始拉动弹簧]

    D --> E[更新弹簧视觉效果]
    E --> F[更新弹珠位置]
    F --> G[用户释放弹簧]

    G --> H[计算弹射力度]
    H --> I[消耗代币]
    I --> J{检查倍数}

    J -->|x1| K[发射单个弹珠]
    J -->|x5| L[间隔发射5个弹珠]
    J -->|x10| M[间隔发射10个弹珠]
    J -->|x20| N[间隔发射20个弹珠]
    J -->|x50| O[间隔发射50个弹珠]

    K --> P[弹珠进入物理世界]
    L --> P
    M --> P
    N --> P
    O --> P

    P --> Q[开始轨迹追踪]
    Q --> R[等待碰撞事件]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#ffebee
    style P fill:#e8f5e8
    style Q fill:#f3e5f5
```

#### 4.1.2 弹簧机制实现
基于现有 `PinballGameStartController` 的弹射实现：

```typescript
export class CakePartyLaunchController {
    private readonly SPRING_MIN_SCALE = 0.6;
    private springState = EPinballSpringState.READY;
    private currentMultiplier = 1;

    // 拉动弹簧
    public pullSpring(power: number): void {
        if (this.springState !== EPinballSpringState.READY) return;

        const scaleY = 1 - (1 - this.SPRING_MIN_SCALE) * power;
        this.spring.scaleY = scaleY;
        this.updateSpringVisual(scaleY);
        this.updateBallPosition(scaleY);
        this.springState = EPinballSpringState.PULLING;
    }

    // 释放弹簧，发射弹珠
    public releaseSpring(): void {
        if (this.springState !== EPinballSpringState.PULLING) return;

        const springPower = 1 - this.spring.scaleY;
        this.springState = EPinballSpringState.RELEASING;

        // 根据倍数创建多个弹珠
        this.launchMultipleBalls(springPower, this.currentMultiplier);

        // 弹簧回弹动画
        this.playSpringReleaseAnimation();
    }

    // 多倍数弹珠发射
    private launchMultipleBalls(power: number, multiplier: number): void {
        for (let i = 0; i < multiplier; i++) {
            setTimeout(() => {
                const ball = this.createBall();
                const launchForce = this.calculateLaunchForce(power, i);
                PhysicsUtil.applyLinearImpulse(ball, launchForce);
            }, i * 100); // 间隔100ms发射
        }
    }

    // 计算发射力度（添加随机性）
    private calculateLaunchForce(power: number, ballIndex: number): XY {
        const baseForce = power * pinballPhysicsConfig.springMaxPower;
        const randomX = randomRange(-0.1, 0.1);
        const randomY = randomRange(-0.05, 0.05);

        return {
            x: randomX + (ballIndex * 0.02), // 轻微偏移避免重叠
            y: -baseForce + randomY
        };
    }
}
```

#### 4.1.2 倍数选择逻辑
```typescript
export class MultiplierController {
    private multipliers = [1, 5, 10, 20, 50];
    private currentMultiplier = 1;
    private tokenBalance = 0;

    // 自动选择倍数（PRD需求）
    public autoSelectMultiplier(balance: number): number {
        this.tokenBalance = balance;

        if (balance >= 50) return this.setMultiplier(50);
        if (balance >= 20) return this.setMultiplier(20);
        if (balance >= 10) return this.setMultiplier(10);
        if (balance >= 5) return this.setMultiplier(5);
        return this.setMultiplier(1);
    }

    // 手动切换倍数
    public cycleMultiplier(): number {
        const currentIndex = this.multipliers.indexOf(this.currentMultiplier);
        const nextIndex = (currentIndex + 1) % this.multipliers.length;
        const nextMultiplier = this.multipliers[nextIndex];

        return this.setMultiplier(nextMultiplier);
    }

    // 验证是否可以发射
    public canLaunch(): boolean {
        return this.tokenBalance >= this.currentMultiplier;
    }

    // 消耗代币
    public consumeTokens(): boolean {
        if (!this.canLaunch()) return false;

        this.tokenBalance -= this.currentMultiplier;
        SmartEvent.dispatchEventWith(ECakePartyEventType.TOKEN_CONSUMED, {
            consumed: this.currentMultiplier,
            remaining: this.tokenBalance
        });
        return true;
    }
}
```

### 4.2 撞击器系统实现

#### 4.2.1 弹珠碰撞处理流程图

```mermaid
flowchart TD
    A[弹珠与撞击器碰撞] --> B{撞击器是否激活}
    B -->|未激活| C[忽略碰撞]
    B -->|已激活| D[检查弹珠撞击次数]

    D --> E{撞击次数 < 10}
    E -->|否| F[停用所有撞击器]
    E -->|是| G[记录撞击]

    G --> H[计算得分增量]
    H --> I[播放撞击特效]
    I --> J[播放音效]
    J --> K[更新分数显示]

    K --> L{检查连击}
    L -->|有连击| M[显示连击效果]
    L -->|无连击| N[继续游戏]

    F --> O[播放停用特效]
    O --> P[弹珠继续运动]

    M --> N
    P --> N
    C --> N

    style A fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#ffebee
    style G fill:#e8f5e8
    style L fill:#f3e5f5
```

#### 4.2.2 计分撞击器实现
```typescript
export class ScoringBumperController {
    private bumpers: Map<string, IScoringBumper> = new Map();
    private readonly MAX_HITS_PER_BALL = 10; // PRD需求：最多10次撞击

    // 创建计分撞击器
    public createScoringBumper(config: IBumperConfig, index: number): void {
        const bumper = this.physicsCtr.createCircle({
            radius: config.radius,
            center: config.position,
            bodyType: b2BodyType.b2_staticBody,
            restitution: 1.2, // 弹性系数>1，产生加速效果
            userData: {
                id: `scoring_bumper_${index}`,
                callback: this.onBumperHit.bind(this),
                bumperIndex: index
            }
        });

        this.bumpers.set(`bumper_${index}`, {
            body: bumper,
            hitCount: 0,
            isActive: true,
            config
        });
    }

    // 撞击器碰撞处理
    private onBumperHit(bumperIndex: number, ball: b2Body): void {
        const bumperKey = `bumper_${bumperIndex}`;
        const bumper = this.bumpers.get(bumperKey);

        if (!bumper || !bumper.isActive) return;

        // 检查该球的撞击次数
        const ballHits = this.getBallHitCount(ball);
        if (ballHits >= this.MAX_HITS_PER_BALL) {
            this.deactivateBumpers(ball);
            return;
        }

        // 记录撞击
        this.recordBumperHit(ball, bumperIndex);

        // 播放撞击效果
        this.playBumperHitEffect(bumperIndex);

        // 分发撞击事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.BUMPER_HIT, {
            bumperIndex,
            ballId: ball.GetUserData().ballId,
            hitCount: ballHits + 1
        });
    }

    // 停用撞击器（达到最大撞击次数）
    private deactivateBumpers(ball: b2Body): void {
        const ballId = ball.GetUserData().ballId;

        this.bumpers.forEach((bumper, key) => {
            if (bumper.isActive) {
                bumper.isActive = false;
                this.playBumperDeactivateEffect(key);
            }
        });

        SmartEvent.dispatchEventWith(ECakePartyEventType.BUMPERS_DEACTIVATED, { ballId });
    }
}
```

#### 4.2.2 奖励区域系统
```typescript
export class BonusZoneController {
    private bonusZones: IBonusZone[] = [];

    // 创建奖励区域
    public createBonusZones(configs: IBonusZoneConfig[]): void {
        configs.forEach((config, index) => {
            const sensor = this.physicsCtr.createBox({
                width: config.width,
                height: config.height,
                center: config.position,
                isSensor: true, // 传感器，不产生物理碰撞
                userData: {
                    id: `bonus_zone_${index}`,
                    callback: this.onBallEnterBonusZone.bind(this),
                    zoneIndex: index,
                    multiplier: config.multiplier
                }
            });

            this.bonusZones.push({
                body: sensor,
                multiplier: config.multiplier,
                index,
                config
            });
        });
    }

    // 弹珠进入奖励区域
    private onBallEnterBonusZone(zoneIndex: number, ball: b2Body): void {
        const zone = this.bonusZones[zoneIndex];
        if (!zone) return;

        const ballData = ball.GetUserData();

        // 计算该球的最终得分
        const finalScore = this.calculateFinalScore(ballData, zone.multiplier);

        // 播放奖励区域效果
        this.playBonusZoneEffect(zoneIndex, finalScore);

        // 分发得分事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.BALL_SCORED, {
            ballId: ballData.ballId,
            zoneIndex,
            zoneMultiplier: zone.multiplier,
            finalScore
        });

        // 移除弹珠
        this.removeBall(ball);
    }
}
```

### 4.3 分数计算系统

#### 4.3.1 分数计算流程图

```mermaid
flowchart TD
    A[弹珠进入奖励区域] --> B[获取弹珠撞击记录]
    B --> C[计算基础分数]
    C --> D[应用奖励区域倍数]

    D --> E[计算最终得分]
    E --> F[更新个人分数]
    F --> G[累积团队总分]

    G --> H{检查里程碑}
    H -->|达成里程碑| I[触发里程碑事件]
    H -->|未达成| J[更新进度显示]

    I --> K[播放里程碑特效]
    K --> L[显示奖励弹窗]
    L --> M{检查蛋糕完成}

    M -->|蛋糕完成| N[触发蛋糕完成事件]
    M -->|未完成| O[继续游戏]

    N --> P[播放完成庆祝]
    P --> Q{检查所有蛋糕}

    Q -->|全部完成| R[触发大奖事件]
    Q -->|未全部完成| S[切换到下个蛋糕]

    J --> O
    S --> O
    R --> T[游戏结束]

    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style I fill:#fff3e0
    style N fill:#f3e5f5
    style R fill:#ffebee
```

#### 4.3.2 分数计算核心逻辑
```typescript
export class CakePartyScoreCalculator {
    private ballScores: Map<string, IBallScore> = new Map();

    // 记录弹珠撞击
    public recordBumperHit(ballId: string, multiplier: number): void {
        const ballScore = this.getBallScore(ballId);
        ballScore.bumperHits++;
        ballScore.bumperScore += multiplier;

        // 限制最大撞击次数
        if (ballScore.bumperHits >= 10) {
            ballScore.maxHitsReached = true;
        }

        this.ballScores.set(ballId, ballScore);
    }

    // 计算最终得分
    public calculateFinalScore(ballId: string, bonusMultiplier: number): number {
        const ballScore = this.getBallScore(ballId);

        // PRD公式：基础分数 = 撞击器得分总和，最终分数 = 基础分数 × 奖励区域倍数
        const baseScore = ballScore.bumperScore;
        const finalScore = baseScore * bonusMultiplier;

        ballScore.finalScore = finalScore;
        ballScore.bonusMultiplier = bonusMultiplier;

        return finalScore;
    }

    // 获取弹珠得分记录
    private getBallScore(ballId: string): IBallScore {
        if (!this.ballScores.has(ballId)) {
            this.ballScores.set(ballId, {
                ballId,
                bumperHits: 0,
                bumperScore: 0,
                bonusMultiplier: 1,
                finalScore: 0,
                maxHitsReached: false
            });
        }
        return this.ballScores.get(ballId);
    }

    // 累积团队总分
    public addToTeamScore(score: number, cakeIndex: number): void {
        const teamManager = CakePartyTeamManager.getInstance();
        teamManager.addScore(cakeIndex, score);

        // 检查里程碑
        this.checkMilestone(cakeIndex);
    }
}
```

### 4.4 游戏桌面配置系统

#### 4.4.1 桌面元素管理
基于现有 `PinballGameTableController` 扩展：

```typescript
export class CakePartyTableController extends PinballGameTableController {
    private scoringBumpers: IScoringBumperConfig[] = [];
    private bonusZones: IBonusZoneConfig[] = [];

    public init() {
        super.init(); // 初始化基础桌面元素（边界、挡板等）
        this.initScoringElements();
        this.initBonusSystem();
        this.initSpecialEffects();
    }

    // 初始化计分元素
    private initScoringElements(): void {
        // 创建计分撞击器
        this.scoringBumpers = this.loadScoringBumperConfig();
        this.scoringBumpers.forEach((config, index) => {
            this.createScoringBumper(config, index);
        });

        // 创建橡胶挡板（现有实现扩展）
        this.initEnhancedRubbers();
    }

    // 初始化奖励系统
    private initBonusSystem(): void {
        this.bonusZones = this.loadBonusZoneConfig();
        this.bonusZones.forEach((config, index) => {
            this.createBonusZone(config, index);
        });
    }

    // 增强版橡胶挡板（添加防卡球机制）
    private initEnhancedRubbers(): void {
        PinballTableMap.rubbers.forEach((point, index) => {
            this.physicsCtr.createCircle({
                radius: 36,
                center: { x: point.x, y: point.y },
                friction: 0.2,
                restitution: 1,
                userData: {
                    id: `rubber_${index}`,
                    callback: () => {
                        // 防卡球机制：边缘橡胶给予侧向力
                        this.applyAntiStuckForce(index);

                        // 播放撞击音效
                        SoundController.playEffect('rubber_hit');
                    }
                }
            });
        });
    }

    // 防卡球力度应用
    private applyAntiStuckForce(rubberIndex: number): void {
        const totalRubbers = PinballTableMap.rubbers.length;

        if (rubberIndex === 0) {
            // 最左侧橡胶：向右推
            PhysicsUtil.applyLinearImpulse(this.view.ball, {
                x: randomRange(0.15, 0.25), y: 0
            });
        } else if (rubberIndex === totalRubbers - 1) {
            // 最右侧橡胶：向左推
            PhysicsUtil.applyLinearImpulse(this.view.ball, {
                x: -randomRange(0.15, 0.25), y: 0
            });
        }
    }
}
```

#### 4.4.2 动态难度调整
```typescript
export class DifficultyController {
    private currentDifficulty = 1;
    private playerPerformance: IPerformanceData;

    // 根据玩家表现调整难度
    public adjustDifficulty(gameResults: IGameResult[]): void {
        const avgScore = this.calculateAverageScore(gameResults);
        const targetScore = this.getTargetScore();

        if (avgScore > targetScore * 1.2) {
            this.increaseDifficulty();
        } else if (avgScore < targetScore * 0.8) {
            this.decreaseDifficulty();
        }
    }

    // 应用难度设置
    public applyDifficultySettings(): void {
        const settings = this.getDifficultySettings(this.currentDifficulty);

        // 调整撞击器弹性
        this.adjustBumperRestitution(settings.bumperRestitution);

        // 调整奖励区域大小
        this.adjustBonusZoneSize(settings.bonusZoneScale);

        // 调整弹珠物理参数
        this.adjustBallPhysics(settings.ballSettings);
    }
}
```

### 4.5 性能优化实现

#### 4.5.1 对象池管理
```typescript
export class PinballObjectPool {
    private ballPool: b2Body[] = [];
    private effectPool: egret.DisplayObject[] = [];
    private readonly MAX_BALLS = 50; // 最大弹珠数量

    // 获取弹珠对象
    public getBall(): b2Body {
        if (this.ballPool.length > 0) {
            const ball = this.ballPool.pop();
            this.resetBall(ball);
            return ball;
        }

        return this.createNewBall();
    }

    // 回收弹珠对象
    public recycleBall(ball: b2Body): void {
        if (this.ballPool.length < this.MAX_BALLS) {
            ball.SetEnabled(false);
            this.ballPool.push(ball);
        } else {
            // 池满时销毁对象
            this.physicsCtr.world.DestroyBody(ball);
        }
    }

    // 重置弹珠状态
    private resetBall(ball: b2Body): void {
        ball.SetEnabled(true);
        ball.SetLinearVelocity({ x: 0, y: 0 });
        ball.SetAngularVelocity(0);
        ball.GetUserData().ballId = this.generateBallId();
    }
}
```

#### 4.5.2 物理引擎优化
```typescript
export class PhysicsOptimizer {
    private activeBalls: Set<b2Body> = new Set();
    private readonly MAX_ACTIVE_BALLS = 10;

    // 限制活跃弹珠数量
    public manageBallCount(): void {
        if (this.activeBalls.size > this.MAX_ACTIVE_BALLS) {
            const oldestBall = this.getOldestBall();
            this.forceRemoveBall(oldestBall);
        }
    }

    // 动态调整物理精度
    public adjustPhysicsAccuracy(ballCount: number): void {
        let velocityIterations = 4;
        let positionIterations = 3;

        if (ballCount > 5) {
            velocityIterations = 3;
            positionIterations = 2;
        }

        this.physicsCtr.setIterations(velocityIterations, positionIterations);
    }

    // 休眠管理
    public manageSleeping(): void {
        this.activeBalls.forEach(ball => {
            const velocity = ball.GetLinearVelocity();
            const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);

            if (speed < 0.1) {
                ball.SetSleepingAllowed(true);
            }
        });
    }
}
```

## 5. 数据模型设计

### 5.1 核心数据结构

#### 5.1.1 游戏状态模型
```typescript
interface ICakePartyGameState {
    // 代币系统
    tokenBalance: number;
    tokenEarned: number;
    tokenConsumed: number;

    // 倍数系统
    currentMultiplier: number;
    availableMultipliers: number[];

    // 团队进度
    teamProgress: ICakeProgress[];
    currentCake: number;

    // 分数系统
    currentGameScore: number;
    totalScore: number;
    highScore: number;

    // 游戏状态
    gamePhase: EGamePhase;
    ballsInPlay: number;
    isGameActive: boolean;
}

interface ICakeProgress {
    cakeId: number;
    cakeType: ECakeType;
    currentStage: number;
    totalStages: number;
    requiredScore: number;
    currentScore: number;
    teamMember: ITeamMember;
    isCompleted: boolean;
    rewards: IReward[];
    milestones: IMilestone[];
}
```

#### 5.1.2 物理配置模型
```typescript
interface IPinballPhysicsConfig {
    // 世界参数
    gravity: number;
    simulateSpeed: number;
    worldScale: number;

    // 弹珠参数
    ballRadius: number;
    ballDensity: number;
    ballFriction: number;
    ballRestitution: number;

    // 弹射参数
    springMaxPower: number;
    springMinScale: number;
    launchAngleVariance: number;

    // 撞击器参数
    bumperRadius: number;
    bumperRestitution: number;
    maxBumperHits: number;

    // 性能参数
    maxActiveBalls: number;
    velocityIterations: number;
    positionIterations: number;
}
```

#### 5.1.3 弹珠数据模型
```typescript
interface IBallScore {
    ballId: string;
    launchTime: number;
    multiplier: number;

    // 撞击记录
    bumperHits: number;
    bumperScore: number;
    maxHitsReached: boolean;

    // 最终得分
    bonusMultiplier: number;
    finalScore: number;

    // 轨迹数据（用于回放）
    trajectory: ITrajectoryPoint[];
}

interface ITrajectoryPoint {
    timestamp: number;
    position: XY;
    velocity: XY;
    event?: string; // 'bumper_hit', 'bonus_zone', etc.
}
```

#### 5.1.4 游戏配置模型
```typescript
interface IScoringBumperConfig {
    id: string;
    position: XY;
    radius: number;
    restitution: number;
    scoreValue: number;
    effectType: string;
    soundEffect: string;
}

interface IBonusZoneConfig {
    id: string;
    position: XY;
    width: number;
    height: number;
    multiplier: number;
    probability: number; // 弹珠落入概率权重
    visualEffect: string;
    rewardType: ERewardType;
}
```

## 6. 视图层设计

### 6.1 弹珠游戏视图架构

#### 6.1.1 主视图控制器
```typescript
export class CakePartyPinballView extends PinballView {
    // 继承现有弹珠视图，扩展Cake Party特性
    public multiplierPanel: MultiplierPanel;
    public scorePanel: CakePartyScorePanel;
    public teamProgressPanel: TeamProgressPanel;
    public tokenDisplay: TokenDisplay;
    public cakeProgressDisplay: CakeProgressDisplay;

    // 游戏控制器
    private launchController: CakePartyLaunchController;
    private scoreController: CakePartyScoreController;
    private effectController: CakePartyEffectController;

    protected createChildren() {
        super.createChildren(); // 继承基础弹珠桌面
        this.initCakePartyUI();
        this.initControllers();
        this.setupEventListeners();
    }

    private initCakePartyUI(): void {
        // 初始化Cake Party特有UI组件
        this.multiplierPanel = new MultiplierPanel();
        this.scorePanel = new CakePartyScorePanel();
        this.teamProgressPanel = new TeamProgressPanel();
        this.tokenDisplay = new TokenDisplay();
        this.cakeProgressDisplay = new CakeProgressDisplay();

        this.addChild(this.multiplierPanel);
        this.addChild(this.scorePanel);
        this.addChild(this.teamProgressPanel);
        this.addChild(this.tokenDisplay);
        this.addChild(this.cakeProgressDisplay);
    }

    private initControllers(): void {
        this.launchController = new CakePartyLaunchController(this);
        this.scoreController = new CakePartyScoreController(this);
        this.effectController = new CakePartyEffectController(this);

        this.launchController.init();
        this.scoreController.init();
        this.effectController.init();
    }
}
```

#### 6.1.2 倍数选择面板
```typescript
export class MultiplierPanel extends eui.Component {
    public multiplierButton: eui.Button;
    public multiplierLabel: eui.Label;
    public tokenBalanceLabel: eui.Label;
    public insufficientTokensWarning: eui.Group;

    private currentMultiplier = 1;
    private availableMultipliers = [1, 5, 10, 20, 50];
    private tokenBalance = 0;

    protected createChildren() {
        super.createChildren();
        this.skinName = 'MultiplierPanelSkin';
        this.setupButtonEvents();
    }

    private setupButtonEvents(): void {
        this.multiplierButton.addEventListener(egret.TouchEvent.TOUCH_TAP,
            this.onMultiplierButtonTap, this);
    }

    private onMultiplierButtonTap(): void {
        const nextMultiplier = this.getNextMultiplier();
        this.setMultiplier(nextMultiplier);

        // 播放切换音效
        SoundController.playEffect('multiplier_switch');
    }

    public setMultiplier(multiplier: number): void {
        this.currentMultiplier = multiplier;
        this.multiplierLabel.text = `x${multiplier}`;

        // 检查代币是否足够
        const canAfford = this.tokenBalance >= multiplier;
        this.updateButtonState(canAfford);

        // 分发倍数变更事件
        SmartEvent.dispatchEventWith(ECakePartyEventType.MULTIPLIER_CHANGED, {
            multiplier,
            canAfford
        });
    }

    public updateTokenBalance(balance: number): void {
        this.tokenBalance = balance;
        this.tokenBalanceLabel.text = balance.toString();

        // 自动选择合适的倍数（PRD需求）
        const autoMultiplier = this.calculateAutoMultiplier(balance);
        if (autoMultiplier !== this.currentMultiplier) {
            this.setMultiplier(autoMultiplier);
        }
    }

    private calculateAutoMultiplier(balance: number): number {
        if (balance >= 50) return 50;
        if (balance >= 20) return 20;
        if (balance >= 10) return 10;
        if (balance >= 5) return 5;
        return 1;
    }

    private updateButtonState(canAfford: boolean): void {
        this.multiplierButton.enabled = canAfford;
        this.insufficientTokensWarning.visible = !canAfford;

        if (canAfford) {
            this.multiplierButton.alpha = 1.0;
        } else {
            this.multiplierButton.alpha = 0.5;
        }
    }
}
```

#### 6.1.3 分数显示系统
```typescript
export class CakePartyScorePanel extends eui.Component {
    public currentScoreLabel: eui.Label;
    public totalScoreLabel: eui.Label;
    public scoreIncrementLabel: eui.Label;
    public comboLabel: eui.Label;

    private currentScore = 0;
    private totalScore = 0;
    private comboCount = 0;

    protected createChildren() {
        super.createChildren();
        this.skinName = 'CakePartyScorePanelSkin';
        this.initAnimations();
    }

    // 更新当前游戏分数
    public updateCurrentScore(newScore: number, increment: number): void {
        this.currentScore = newScore;

        // 播放分数增加动画
        this.playScoreIncrementAnimation(increment);

        // 更新显示
        this.currentScoreLabel.text = newScore.toString();
    }

    // 更新总分数
    public updateTotalScore(newTotal: number): void {
        const oldTotal = this.totalScore;
        this.totalScore = newTotal;

        // 播放总分数增加动画
        this.playTotalScoreAnimation(oldTotal, newTotal);
    }

    // 显示连击
    public showCombo(combo: number): void {
        if (combo <= 1) {
            this.comboLabel.visible = false;
            return;
        }

        this.comboCount = combo;
        this.comboLabel.text = `COMBO x${combo}`;
        this.comboLabel.visible = true;

        // 播放连击动画
        this.playComboAnimation();
    }

    private playScoreIncrementAnimation(increment: number): void {
        this.scoreIncrementLabel.text = `+${increment}`;
        this.scoreIncrementLabel.visible = true;
        this.scoreIncrementLabel.alpha = 1;
        this.scoreIncrementLabel.scaleX = this.scoreIncrementLabel.scaleY = 1;

        egret.Tween.get(this.scoreIncrementLabel)
            .to({
                alpha: 0,
                scaleX: 1.5,
                scaleY: 1.5,
                y: this.scoreIncrementLabel.y - 50
            }, 800)
            .call(() => {
                this.scoreIncrementLabel.visible = false;
                this.scoreIncrementLabel.y += 50; // 重置位置
            });
    }

    private playTotalScoreAnimation(oldScore: number, newScore: number): void {
        const duration = 1000;
        const startTime = egret.getTimer();

        const updateScore = () => {
            const elapsed = egret.getTimer() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentDisplayScore = Math.floor(oldScore + (newScore - oldScore) * progress);
            this.totalScoreLabel.text = currentDisplayScore.toString();

            if (progress < 1) {
                egret.setTimeout(updateScore, this, 16); // ~60fps
            }
        };

        updateScore();
    }
}
```

### 6.2 特效系统设计

#### 6.2.1 撞击特效控制器
```typescript
export class CakePartyEffectController {
    private effectPool: Map<string, egret.DisplayObject[]> = new Map();
    private activeEffects: Set<egret.DisplayObject> = new Set();

    public init(): void {
        this.preloadEffects();
        this.setupEventListeners();
    }

    private setupEventListeners(): void {
        SmartEvent.addEventListener(ECakePartyEventType.BUMPER_HIT,
            this.onBumperHit, this);
        SmartEvent.addEventListener(ECakePartyEventType.BALL_SCORED,
            this.onBallScored, this);
        SmartEvent.addEventListener(ECakePartyEventType.MILESTONE_REACHED,
            this.onMilestoneReached, this);
    }

    // 撞击器特效
    private onBumperHit(event: egret.Event): void {
        const { bumperIndex, hitCount } = event.data;
        const bumperPosition = this.getBumperPosition(bumperIndex);

        // 播放撞击特效
        this.playEffect('bumper_hit', bumperPosition);

        // 显示分数飞字
        this.showScorePopup(hitCount, bumperPosition);

        // 播放音效
        SoundController.playEffect('bumper_hit', { volume: 0.7 });
    }

    // 得分特效
    private onBallScored(event: egret.Event): void {
        const { zoneIndex, finalScore } = event.data;
        const zonePosition = this.getBonusZonePosition(zoneIndex);

        // 播放奖励区域特效
        this.playEffect('bonus_zone_hit', zonePosition);

        // 显示最终得分
        this.showFinalScorePopup(finalScore, zonePosition);

        // 播放得分音效
        SoundController.playEffect('score_achieved', { volume: 0.8 });
    }

    // 里程碑特效
    private onMilestoneReached(event: egret.Event): void {
        const { cakeIndex, milestone } = event.data;

        // 播放里程碑达成特效
        this.playFullScreenEffect('milestone_achieved');

        // 显示奖励弹窗
        this.showMilestoneRewardPopup(milestone);

        // 播放庆祝音效
        SoundController.playEffect('milestone_fanfare', { volume: 1.0 });
    }

    private playEffect(effectType: string, position: XY): void {
        const effect = this.getEffectFromPool(effectType);
        if (!effect) return;

        effect.x = position.x;
        effect.y = position.y;
        this.view.addChild(effect);
        this.activeEffects.add(effect);

        // 播放动画
        if (effect instanceof egret.MovieClip) {
            effect.gotoAndPlay(1, 1);
            effect.addEventListener(egret.Event.COMPLETE, () => {
                this.recycleEffect(effectType, effect);
            }, this);
        }
    }
}
```

## 7. 事件系统设计

### 7.1 游戏事件定义

```typescript
enum ECakePartyEventType {
    // 弹珠生命周期
    BALL_CREATED = 'ball_created',
    BALL_LAUNCHED = 'ball_launched',
    BALL_HIT_BUMPER = 'ball_hit_bumper',
    BALL_ENTER_BONUS_ZONE = 'ball_enter_bonus_zone',
    BALL_REMOVED = 'ball_removed',
    BALL_SCORED = 'ball_scored',

    // 撞击器系统
    BUMPER_HIT = 'bumper_hit',
    BUMPERS_DEACTIVATED = 'bumpers_deactivated',
    MAX_HITS_REACHED = 'max_hits_reached',

    // 分数系统
    SCORE_UPDATED = 'score_updated',
    SCORE_INCREMENT = 'score_increment',
    COMBO_ACHIEVED = 'combo_achieved',
    HIGH_SCORE_BEATEN = 'high_score_beaten',

    // 里程碑系统
    MILESTONE_REACHED = 'milestone_reached',
    CAKE_STAGE_COMPLETED = 'cake_stage_completed',
    CAKE_COMPLETED = 'cake_completed',
    ALL_CAKES_COMPLETED = 'all_cakes_completed',

    // 倍数系统
    MULTIPLIER_CHANGED = 'multiplier_changed',
    MULTIPLIER_AUTO_SELECTED = 'multiplier_auto_selected',

    // 代币系统
    TOKEN_CONSUMED = 'token_consumed',
    TOKEN_INSUFFICIENT = 'token_insufficient',
    TOKEN_BALANCE_UPDATED = 'token_balance_updated',

    // 团队系统
    TEAM_PROGRESS_UPDATED = 'team_progress_updated',
    FRIEND_SCORE_ADDED = 'friend_score_added',
    TEAM_MEMBER_ONLINE = 'team_member_online',

    // 游戏状态
    GAME_STARTED = 'game_started',
    GAME_PAUSED = 'game_paused',
    GAME_RESUMED = 'game_resumed',
    GAME_ENDED = 'game_ended',

    // UI事件
    UI_ANIMATION_COMPLETE = 'ui_animation_complete',
    POPUP_SHOWN = 'popup_shown',
    POPUP_CLOSED = 'popup_closed'
}
```

### 7.2 事件处理架构

#### 7.2.1 事件处理流程图

```mermaid
flowchart TD
    A[游戏事件触发] --> B[事件队列管理器]
    B --> C{事件优先级}

    C -->|高优先级| D[立即处理]
    C -->|普通优先级| E[加入处理队列]

    D --> F[事件处理器分发]
    E --> G[按帧限制处理]
    G --> F

    F --> H{事件类型}

    H -->|弹珠事件| I[弹珠事件处理器]
    H -->|分数事件| J[分数事件处理器]
    H -->|UI事件| K[UI事件处理器]
    H -->|音效事件| L[音效事件处理器]

    I --> M[更新物理状态]
    J --> N[更新分数显示]
    K --> O[更新UI组件]
    L --> P[播放音效]

    M --> Q[触发后续事件]
    N --> Q
    O --> Q
    P --> Q

    Q --> R[事件处理完成]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style F fill:#e8f5e8
    style H fill:#f3e5f5
```

#### 7.2.2 主事件处理器
```typescript
export class CakePartyEventHandler {
    private eventMap: Map<string, Function> = new Map();
    private eventQueue: IEventData[] = [];
    private isProcessing = false;

    public init(): void {
        this.registerEventHandlers();
        this.startEventProcessing();
    }

    private registerEventHandlers(): void {
        // 弹珠事件
        this.eventMap.set(ECakePartyEventType.BALL_LAUNCHED, this.onBallLaunched.bind(this));
        this.eventMap.set(ECakePartyEventType.BALL_HIT_BUMPER, this.onBallHitBumper.bind(this));
        this.eventMap.set(ECakePartyEventType.BALL_SCORED, this.onBallScored.bind(this));

        // 分数事件
        this.eventMap.set(ECakePartyEventType.SCORE_UPDATED, this.onScoreUpdated.bind(this));
        this.eventMap.set(ECakePartyEventType.MILESTONE_REACHED, this.onMilestoneReached.bind(this));

        // 倍数事件
        this.eventMap.set(ECakePartyEventType.MULTIPLIER_CHANGED, this.onMultiplierChanged.bind(this));

        // 代币事件
        this.eventMap.set(ECakePartyEventType.TOKEN_CONSUMED, this.onTokenConsumed.bind(this));

        // 注册所有事件监听
        this.eventMap.forEach((handler, eventType) => {
            SmartEvent.addEventListener(eventType, handler, this);
        });
    }

    // 弹珠发射事件处理
    private onBallLaunched(event: egret.Event): void {
        const { ballId, multiplier, launchPower } = event.data;

        // 记录发射数据
        this.recordBallLaunch(ballId, multiplier, launchPower);

        // 更新UI状态
        this.updateLaunchUI(multiplier);

        // 播放发射音效
        SoundController.playEffect('ball_launch');

        // 开始球的轨迹追踪
        this.startBallTracking(ballId);
    }

    // 撞击器碰撞事件处理
    private onBallHitBumper(event: egret.Event): void {
        const { ballId, bumperIndex, hitCount, scoreIncrement } = event.data;

        // 更新分数
        this.updateScore(scoreIncrement);

        // 检查连击
        this.checkCombo(ballId, hitCount);

        // 播放撞击特效
        this.playBumperEffect(bumperIndex, scoreIncrement);

        // 检查是否达到最大撞击次数
        if (hitCount >= 10) {
            SmartEvent.dispatchEventWith(ECakePartyEventType.MAX_HITS_REACHED, { ballId });
        }
    }

    // 得分事件处理
    private onBallScored(event: egret.Event): void {
        const { ballId, finalScore, zoneIndex, zoneMultiplier } = event.data;

        // 更新总分
        this.addToTotalScore(finalScore);

        // 检查里程碑
        this.checkMilestones(finalScore);

        // 播放得分特效
        this.playScoreEffect(finalScore, zoneIndex);

        // 记录得分数据
        this.recordBallScore(ballId, finalScore, zoneMultiplier);
    }

    // 里程碑达成事件处理
    private onMilestoneReached(event: egret.Event): void {
        const { cakeIndex, milestone, rewards } = event.data;

        // 播放里程碑特效
        this.playMilestoneEffect(milestone);

        // 显示奖励
        this.showMilestoneRewards(rewards);

        // 更新蛋糕进度
        this.updateCakeProgress(cakeIndex, milestone);

        // 检查是否完成蛋糕
        if (milestone.isComplete) {
            SmartEvent.dispatchEventWith(ECakePartyEventType.CAKE_COMPLETED, { cakeIndex });
        }
    }
}
```

#### 7.2.2 事件队列管理
```typescript
export class EventQueueManager {
    private eventQueue: IQueuedEvent[] = [];
    private isProcessing = false;
    private readonly MAX_EVENTS_PER_FRAME = 5;

    // 添加事件到队列
    public queueEvent(eventType: string, data: any, priority: number = 0): void {
        const event: IQueuedEvent = {
            type: eventType,
            data,
            priority,
            timestamp: egret.getTimer()
        };

        // 按优先级插入
        this.insertByPriority(event);

        // 开始处理队列
        if (!this.isProcessing) {
            this.processQueue();
        }
    }

    // 处理事件队列
    private processQueue(): void {
        if (this.eventQueue.length === 0) {
            this.isProcessing = false;
            return;
        }

        this.isProcessing = true;
        let processedCount = 0;

        while (this.eventQueue.length > 0 && processedCount < this.MAX_EVENTS_PER_FRAME) {
            const event = this.eventQueue.shift();
            this.dispatchEvent(event);
            processedCount++;
        }

        // 下一帧继续处理
        if (this.eventQueue.length > 0) {
            egret.setTimeout(this.processQueue, this, 16);
        } else {
            this.isProcessing = false;
        }
    }

    private dispatchEvent(event: IQueuedEvent): void {
        try {
            SmartEvent.dispatchEventWith(event.type, event.data);
        } catch (error) {
            console.error(`Error dispatching event ${event.type}:`, error);
        }
    }
}
```

## 8. 性能优化策略

### 8.1 Box2D物理引擎优化

#### 8.1.1 物理引擎优化流程图

```mermaid
flowchart TD
    A[游戏帧开始] --> B[计算帧时间差]
    B --> C[累积物理时间]
    C --> D{时间 >= 1/60s}

    D -->|否| E[跳过物理更新]
    D -->|是| F[检查活跃弹珠数量]

    F --> G{弹珠数量}
    G -->|> 8个| H[降低迭代精度]
    G -->|4-8个| I[中等迭代精度]
    G -->|< 4个| J[高迭代精度]

    H --> K[速度迭代3次<br/>位置迭代2次]
    I --> L[速度迭代4次<br/>位置迭代3次]
    J --> M[速度迭代6次<br/>位置迭代4次]

    K --> N[执行物理步进]
    L --> N
    M --> N

    N --> O[处理碰撞回调]
    O --> P[更新刚体状态]
    P --> Q[检查休眠对象]

    Q --> R{还有剩余时间}
    R -->|是| S[继续下一步]
    R -->|否| T[结束物理更新]

    S --> D
    E --> T
    T --> U[渲染更新]

    style A fill:#e3f2fd
    style F fill:#fff3e0
    style G fill:#f3e5f5
    style N fill:#e8f5e8
```

#### 8.1.2 物理世界优化实现
```typescript
export class PhysicsOptimizer {
    private readonly OPTIMAL_TIME_STEP = 1/60;
    private readonly MAX_SUB_STEPS = 3;
    private accumulatedTime = 0;

    // 固定时间步长更新
    public updatePhysics(deltaTime: number): void {
        this.accumulatedTime += deltaTime;

        let subSteps = 0;
        while (this.accumulatedTime >= this.OPTIMAL_TIME_STEP && subSteps < this.MAX_SUB_STEPS) {
            this.physicsCtr.world.Step(this.OPTIMAL_TIME_STEP, {
                velocityIterations: this.getOptimalVelocityIterations(),
                positionIterations: this.getOptimalPositionIterations()
            });

            this.accumulatedTime -= this.OPTIMAL_TIME_STEP;
            subSteps++;
        }
    }

    // 动态调整迭代次数
    private getOptimalVelocityIterations(): number {
        const activeBallCount = this.getActiveBallCount();
        if (activeBallCount > 8) return 3;
        if (activeBallCount > 4) return 4;
        return 6;
    }

    private getOptimalPositionIterations(): number {
        const activeBallCount = this.getActiveBallCount();
        if (activeBallCount > 8) return 2;
        if (activeBallCount > 4) return 3;
        return 4;
    }
}
```

#### 8.1.2 碰撞检测优化
```typescript
export class CollisionOptimizer {
    // 碰撞分组优化
    public setupCollisionFilters(): void {
        const BALL_CATEGORY = 0x0001;
        const BUMPER_CATEGORY = 0x0002;
        const WALL_CATEGORY = 0x0004;
        const SENSOR_CATEGORY = 0x0008;

        // 弹珠只与撞击器、墙壁、传感器碰撞
        this.setBallCollisionFilter({
            categoryBits: BALL_CATEGORY,
            maskBits: BUMPER_CATEGORY | WALL_CATEGORY | SENSOR_CATEGORY
        });

        // 撞击器只与弹珠碰撞
        this.setBumperCollisionFilter({
            categoryBits: BUMPER_CATEGORY,
            maskBits: BALL_CATEGORY
        });
    }

    // 空间分区优化
    public enableSpatialPartitioning(): void {
        // Box2D内置的broad-phase已经很高效，主要优化在于合理设置AABB
        this.physicsCtr.world.SetBroadPhaseCallback(this.customBroadPhaseCallback.bind(this));
    }
}
```

### 8.2 渲染性能优化

#### 8.2.1 对象池管理
```typescript
export class RenderObjectPool {
    private pools: Map<string, any[]> = new Map();
    private readonly POOL_SIZES = {
        'score_popup': 20,
        'effect_particle': 50,
        'ball_trail': 10,
        'ui_tween': 30
    };

    // 获取对象
    public getObject<T>(type: string, createFn: () => T): T {
        const pool = this.getPool(type);

        if (pool.length > 0) {
            return pool.pop() as T;
        }

        return createFn();
    }

    // 回收对象
    public recycleObject(type: string, obj: any): void {
        const pool = this.getPool(type);
        const maxSize = this.POOL_SIZES[type] || 10;

        if (pool.length < maxSize) {
            this.resetObject(obj);
            pool.push(obj);
        }
    }

    private resetObject(obj: any): void {
        if (obj instanceof egret.DisplayObject) {
            obj.visible = false;
            obj.alpha = 1;
            obj.scaleX = obj.scaleY = 1;
            obj.rotation = 0;
        }
    }
}
```

#### 8.2.2 批量渲染优化
```typescript
export class BatchRenderer {
    private renderQueue: IRenderCommand[] = [];
    private readonly MAX_BATCH_SIZE = 100;

    // 批量更新分数显示
    public batchUpdateScores(updates: IScoreUpdate[]): void {
        const batches = this.createBatches(updates, this.MAX_BATCH_SIZE);

        batches.forEach((batch, index) => {
            egret.setTimeout(() => {
                this.processBatch(batch);
            }, this, index * 16); // 分帧处理
        });
    }

    // 合并相似的渲染操作
    public optimizeRenderCommands(): void {
        const grouped = this.groupSimilarCommands(this.renderQueue);

        grouped.forEach(group => {
            this.executeBatchCommand(group);
        });

        this.renderQueue.length = 0;
    }
}
```

### 8.3 内存管理优化

#### 8.3.1 资源生命周期管理
```typescript
export class ResourceManager {
    private loadedTextures: Map<string, egret.Texture> = new Map();
    private textureUsageCount: Map<string, number> = new Map();
    private readonly MAX_TEXTURE_CACHE = 50;

    // 智能纹理缓存
    public getTexture(key: string): egret.Texture {
        let texture = this.loadedTextures.get(key);

        if (!texture) {
            texture = RES.getRes(key);
            this.cacheTexture(key, texture);
        }

        this.incrementUsage(key);
        return texture;
    }

    // 释放未使用的纹理
    public cleanupUnusedTextures(): void {
        const unusedTextures: string[] = [];

        this.textureUsageCount.forEach((count, key) => {
            if (count === 0) {
                unusedTextures.push(key);
            }
        });

        unusedTextures.forEach(key => {
            this.releaseTexture(key);
        });
    }

    // 内存压力监控
    public monitorMemoryPressure(): void {
        const memoryInfo = (performance as any).memory;
        if (memoryInfo && memoryInfo.usedJSHeapSize > memoryInfo.totalJSHeapSize * 0.8) {
            this.performEmergencyCleanup();
        }
    }
}
```

#### 8.3.2 音效管理优化
```typescript
export class AudioManager {
    private audioPool: Map<string, egret.Sound[]> = new Map();
    private playingAudios: Set<egret.SoundChannel> = new Set();
    private readonly MAX_CONCURRENT_SOUNDS = 8;

    // 限制并发音效数量
    public playEffect(key: string, options?: any): egret.SoundChannel {
        if (this.playingAudios.size >= this.MAX_CONCURRENT_SOUNDS) {
            this.stopOldestSound();
        }

        const sound = this.getSound(key);
        const channel = sound.play(0, 1);

        if (channel) {
            this.playingAudios.add(channel);
            channel.addEventListener(egret.Event.SOUND_COMPLETE, () => {
                this.playingAudios.delete(channel);
            }, this);
        }

        return channel;
    }

    // 音效预加载
    public preloadGameAudio(): Promise<void> {
        const audioKeys = [
            'ball_launch', 'bumper_hit', 'score_achieved',
            'milestone_fanfare', 'rubber_hit', 'multiplier_switch'
        ];

        return Promise.all(audioKeys.map(key => this.loadAudio(key)))
            .then(() => console.log('Game audio preloaded'));
    }
}
```

## 9. 开发计划与风险评估

### 9.1 详细开发阶段

#### 9.1.1 开发时间线甘特图

```mermaid
gantt
    title Cake Party 弹珠游戏开发计划
    dateFormat  YYYY-MM-DD
    section Phase 1: 基础架构
    场景架构搭建           :p1-1, 2024-01-01, 1d
    MVC框架集成           :p1-2, after p1-1, 1d
    事件系统创建           :p1-3, after p1-2, 1d

    section Phase 2: 物理系统
    弹射机制实现           :p2-1, after p1-3, 2d
    撞击器系统             :p2-2, after p2-1, 2d

    section Phase 3: UI开发
    倍数面板              :p3-1, after p2-2, 1d
    分数面板              :p3-2, after p3-1, 1d
    特效系统              :p3-3, after p3-2, 1d

    section Phase 4: 分数集成
    分数计算逻辑           :p4-1, after p3-3, 1d
    团队进度同步           :p4-2, after p4-1, 1d

    section Phase 5: 测试优化
    性能测试              :p5-1, after p4-2, 1d
    集成测试              :p5-2, after p5-1, 1d
    最终调优              :p5-3, after p5-2, 1d
```

#### 9.1.2 开发阶段详情

#### Phase 1: 基础架构搭建 (3天)
- **Day 1**:
  - 创建CakePartyPinballScene基础架构
  - 集成现有PhysicsController
  - 搭建MVC框架结构
- **Day 2**:
  - 实现MultiplierController
  - 创建基础事件系统
  - 设置开发调试工具
- **Day 3**:
  - 集成现有PinballView
  - 实现基础UI框架
  - 完成架构单元测试

#### Phase 2: 弹珠物理系统 (4天)
- **Day 1-2**:
  - 扩展PinballGameStartController
  - 实现多倍数弹射机制
  - 优化弹珠创建和管理
- **Day 3-4**:
  - 实现ScoringBumperController
  - 创建BonusZoneController
  - 完成碰撞检测和分数计算

#### Phase 3: UI界面开发 (3天)
- **Day 1**:
  - 实现MultiplierPanel
  - 创建TokenDisplay组件
- **Day 2**:
  - 开发CakePartyScorePanel
  - 实现TeamProgressPanel
- **Day 3**:
  - 集成特效系统
  - 完成UI动画和交互

#### Phase 4: 分数系统集成 (2天)
- **Day 1**:
  - 实现分数计算逻辑
  - 集成里程碑系统
- **Day 2**:
  - 完成团队进度同步
  - 实现数据持久化

#### Phase 5: 测试优化 (3天)
- **Day 1**:
  - 性能测试和优化
  - 内存泄漏检查
- **Day 2**:
  - 集成测试
  - 边界情况处理
- **Day 3**:
  - 最终调优
  - 文档完善

### 9.2 技术风险与应对策略

#### 9.2.1 风险评估矩阵图

```mermaid
quadrantChart
    title 技术风险评估矩阵
    x-axis 低影响 --> 高影响
    y-axis 低概率 --> 高概率

    quadrant-1 监控区域
    quadrant-2 重点关注
    quadrant-3 接受风险
    quadrant-4 立即处理

    多倍数弹珠同步: [0.8, 0.7]
    Box2D性能调优: [0.9, 0.6]
    内存泄漏: [0.7, 0.5]
    网络同步延迟: [0.6, 0.4]
    跨平台兼容: [0.5, 0.3]
    UI响应延迟: [0.4, 0.2]
    音效播放冲突: [0.3, 0.3]
```

#### 9.2.2 高风险项应对策略
1. **多倍数弹珠物理同步**
   - 风险：大量弹珠同时存在导致性能下降
   - 应对：实现弹珠对象池，限制最大并发数量，分批发射
   - 监控指标：FPS、内存使用率、活跃弹珠数量

2. **Box2D性能调优**
   - 风险：物理计算消耗过多CPU资源
   - 应对：动态调整迭代次数，优化碰撞分组，使用固定时间步长
   - 监控指标：物理更新耗时、CPU使用率

3. **内存管理**
   - 风险：频繁创建销毁对象导致内存泄漏
   - 应对：实现完善的对象池系统，定期内存清理
   - 监控指标：内存使用量、GC频率

#### 9.2.3 中风险项应对策略
1. **网络同步延迟**
   - 风险：团队进度同步不及时
   - 应对：实现本地缓存机制，异步更新策略
   - 监控指标：网络延迟、同步成功率

2. **跨平台兼容性**
   - 风险：不同设备性能差异
   - 应对：实现动态画质调整，性能分级
   - 监控指标：不同设备的FPS表现

### 9.3 质量保证策略

#### 9.3.1 自动化测试
```typescript
// 单元测试示例
describe('CakePartyScoreCalculator', () => {
    it('should calculate correct score with multiplier', () => {
        const calculator = new CakePartyScoreCalculator();
        const result = calculator.calculateFinalScore('ball_1', 2);
        expect(result).toBe(expectedScore);
    });

    it('should limit bumper hits to maximum', () => {
        const calculator = new CakePartyScoreCalculator();
        // 测试最大撞击次数限制
    });
});
```

#### 9.3.2 性能基准测试
```typescript
export class PerformanceBenchmark {
    public runBenchmarks(): void {
        this.benchmarkPhysicsPerformance();
        this.benchmarkRenderingPerformance();
        this.benchmarkMemoryUsage();
    }

    private benchmarkPhysicsPerformance(): void {
        const startTime = performance.now();
        // 模拟50个弹珠同时运行
        for (let i = 0; i < 50; i++) {
            this.simulateBallPhysics();
        }
        const endTime = performance.now();
        console.log(`Physics benchmark: ${endTime - startTime}ms`);
    }
}
```

## 10. 部署与配置管理

### 10.1 资源管理策略

#### 10.1.1 资源分包策略
```json
{
  "groups": [
    {
      "name": "pinball_core",
      "keys": "pinball_table,ball_texture,spring_animation"
    },
    {
      "name": "pinball_effects",
      "keys": "bumper_hit_effect,score_popup,milestone_effect"
    },
    {
      "name": "pinball_audio",
      "keys": "ball_launch_sound,bumper_hit_sound,score_sound"
    }
  ]
}
```

#### 10.1.2 动态加载管理
```typescript
export class ResourceLoader {
    public async loadPinballResources(): Promise<void> {
        // 核心资源优先加载
        await RES.loadGroup('pinball_core');

        // 特效资源后台加载
        RES.loadGroup('pinball_effects');

        // 音效资源按需加载
        this.preloadAudioOnUserInteraction();
    }

    private preloadAudioOnUserInteraction(): void {
        const loadAudio = () => {
            RES.loadGroup('pinball_audio');
            document.removeEventListener('touchstart', loadAudio);
        };
        document.addEventListener('touchstart', loadAudio, { once: true });
    }
}
```

### 10.2 配置管理系统

#### 10.2.1 游戏参数配置
```typescript
interface IPinballGameConfig {
    physics: {
        gravity: number;
        ballDensity: number;
        springPower: number;
    };
    gameplay: {
        maxBumperHits: number;
        multipliers: number[];
        bonusZoneMultipliers: number[];
    };
    performance: {
        maxActiveBalls: number;
        physicsIterations: {
            velocity: number;
            position: number;
        };
    };
    ui: {
        animationDuration: number;
        effectIntensity: number;
    };
}
```

#### 10.2.2 A/B测试支持
```typescript
export class ABTestManager {
    private testConfigs: Map<string, any> = new Map();

    public getConfig(key: string, defaultValue: any): any {
        const testGroup = this.getUserTestGroup();
        const testConfig = this.testConfigs.get(`${key}_${testGroup}`);

        return testConfig !== undefined ? testConfig : defaultValue;
    }

    // 支持的A/B测试项
    public initABTests(): void {
        this.testConfigs.set('spring_power_A', 12.0);
        this.testConfigs.set('spring_power_B', 15.0);

        this.testConfigs.set('bumper_restitution_A', 1.0);
        this.testConfigs.set('bumper_restitution_B', 1.2);
    }
}
```
